# 🌐 分布式监控系统部署指南

## 📋 部署架构

```
服务器A → 监控账号1 → 25个群组
服务器B → 监控账号2 → 25个群组
服务器C → 监控账号3 → 25个群组
服务器D → 监控账号4 → 30个群组
           ↓
    中央服务器Bot (处理所有数据)
```

## 🚀 部署步骤

### 1. 服务器A部署 (监控账号1)

```bash
# 上传文件到服务器A
scp -r server_a/ user@server-a-ip:/home/<USER>/monitor/

# 登录服务器A
ssh user@server-a-ip

# 进入目录
cd /home/<USER>/monitor/

# 安装依赖
pip install telethon

# 修改配置
nano config.py
# 填入监控账号1的API_ID和API_HASH
# 填入SERVER_BOT_USERNAME

# 启动监控
python monitor.py
```

### 2. 服务器B部署 (监控账号2)

```bash
# 上传文件到服务器B
scp -r server_b/ user@server-b-ip:/home/<USER>/monitor/

# 登录服务器B
ssh user@server-b-ip

# 进入目录
cd /home/<USER>/monitor/

# 安装依赖
pip install telethon

# 修改配置
nano config.py
# 填入监控账号2的API_ID和API_HASH
# 填入SERVER_BOT_USERNAME

# 启动监控
python monitor.py
```

### 3. 服务器C部署 (监控账号3)

```bash
# 上传文件到服务器C
scp -r server_c/ user@server-c-ip:/home/<USER>/monitor/

# 登录服务器C
ssh user@server-c-ip

# 进入目录
cd /home/<USER>/monitor/

# 安装依赖
pip install telethon

# 修改配置
nano config.py
# 填入监控账号3的API_ID和API_HASH
# 填入SERVER_BOT_USERNAME

# 启动监控
python monitor.py
```

### 4. 服务器D部署 (监控账号4)

```bash
# 上传文件到服务器D
scp -r server_d/ user@server-d-ip:/home/<USER>/monitor/

# 登录服务器D
ssh user@server-d-ip

# 进入目录
cd /home/<USER>/monitor/

# 安装依赖
pip install telethon

# 修改配置
nano config.py
# 填入监控账号4的API_ID和API_HASH
# 填入SERVER_BOT_USERNAME

# 启动监控
python monitor.py
```

## 🔧 配置文件示例

每个服务器的 `config.py` 需要填入对应的信息：

```python
# 服务器A - config.py
API_ID = '12345678'  # 您的监控账号1的API_ID
API_HASH = 'abcdef1234567890abcdef1234567890'  # 您的监控账号1的API_HASH
SERVER_BOT_USERNAME = '@your_server_bot'  # 您的服务器Bot用户名
```

## 📊 监控分配

| 服务器 | 监控账号 | 群组数量 |
|--------|----------|----------|
| 服务器A | monitor_1 | 25个 |
| 服务器B | monitor_2 | 25个 |
| 服务器C | monitor_3 | 25个 |
| 服务器D | monitor_4 | 30个 |

## 🛡️ 进程管理

### 使用 systemd 服务 (推荐)

为每个服务器创建系统服务：

```bash
# 创建服务文件
sudo nano /etc/systemd/system/telegram-monitor.service
```

```ini
[Unit]
Description=Telegram Monitor Service
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/home/<USER>/monitor
ExecStart=/usr/bin/python3 monitor.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用并启动服务
sudo systemctl enable telegram-monitor
sudo systemctl start telegram-monitor

# 查看状态
sudo systemctl status telegram-monitor

# 查看日志
sudo journalctl -u telegram-monitor -f
```

### 使用 screen 会话

```bash
# 启动screen会话
screen -S telegram-monitor

# 运行监控
python monitor.py

# 分离会话 (Ctrl+A, D)
# 重新连接: screen -r telegram-monitor
```

## 🔍 智能合约识别

### 多重识别机制
- **正则表达式优先**: 首先直接识别文本中的 Solana 合约地址
- **DexScreener API 备用**: 仅在没有直接合约地址时，识别 dexscreener.com 交易对链接
- **智能优先级**: 确保直接合约地址优先于交易对链接

### DexScreener 集成
- **触发条件**: 仅在消息中没有直接合约地址时启用
- **链接识别**: 自动检测 `https://dexscreener.com/solana/[pair_address]` 格式链接
- **API 调用**: 通过官方 API 获取交易对的真实代币地址
- **错误处理**: API 失败时返回 None，不影响其他识别逻辑
- **日志记录**: 详细记录 DexScreener 识别过程

## 🔍 智能群组识别

### 动态群组检测
- **实时识别**: 启动时自动检测账号实际加入的群组
- **群组名称获取**: 显示群组ID和对应的群组名称
- **状态验证**: 检查配置中的群组是否实际加入
- **详细日志**: 完整记录群组加入状态

### 启动日志示例
```
🔍 正在获取已加入的群组信息...
✅ 已加入监控群组: -1001162128920 - Solana Alpha Group
✅ 已加入监控群组: -1001234567890 - Crypto Signals
❌ 未加入监控群组: -1001999999999
📊 配置监控群组: 25 个
✅ 实际加入群组: 24 个
📋 已加入的监控群组列表:
   -1001162128920: Solana Alpha Group
   -1001234567890: Crypto Signals
   ...
```

## 🛡️ 去重保护机制

### 核心特性
- **每群每合约唯一**: 确保每个群组的每个合约只推送一次
- **本地SQLite数据库**: 每个监控账号维护独立的去重数据库
- **自动清理**: 7天后自动清理过期记录，避免数据库过大
- **实时统计**: 显示跳过的重复合约数量

### 数据库文件
每个监控账号会创建独立的去重数据库：
- `monitor_1_sent_contracts.db`
- `monitor_2_sent_contracts.db`
- `monitor_3_sent_contracts.db`
- `monitor_4_sent_contracts.db`

### 工作流程
1. 发现合约 → 检查是否已发送过
2. 如果是重复 → 跳过并记录统计
3. 如果是新合约 → 发送到服务器
4. 发送成功 → 标记为已发送

## 📨 数据格式

服务器Bot会收到以下格式的消息：

### 1. 合约发现
```
CONTRACT|DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263|-1001162128920|1704268800|monitor_1|server_a
```

### 2. 心跳信号 (每5分钟)
```
HEARTBEAT|monitor_1|server_a|18000|15|25
```

### 3. 启动通知
```
MONITOR_START|monitor_1|server_a|25|2024-01-03T10:30:00
```

## 📁 日志管理系统

### 日志文件结构
每个监控服务器会自动创建 `logs/` 目录，日志文件按天分割：

```
server_a/
├── logs/
│   ├── monitor_1.log              # 当前日志
│   ├── monitor_1.log.2024-01-01   # 历史日志
│   ├── monitor_1.log.2024-01-02   # 历史日志
│   └── ...
├── monitor_1_sent_contracts.db    # 去重数据库
└── monitor.py
```

### 自动日志管理
- **按天分割**: 每天午夜自动创建新的日志文件
- **自动清理**: 启动时自动删除超过7天的日志文件
- **文件命名**: `monitor_X.log.YYYY-MM-DD` 格式
- **保留策略**: 只保留最近7天的日志文件

## 🔍 监控和维护

### 查看当前日志
```bash
# 实时查看当前日志
tail -f logs/monitor_1.log

# 查看启动时的群组信息
grep "已加入监控群组\|实际加入群组\|未加入监控群组" logs/monitor_1.log

# 查看错误
grep "ERROR" logs/monitor_1.log

# 查看发现的合约
grep "发现新合约" logs/monitor_1.log

# 查看 DexScreener 识别的合约
grep "从DexScreener获取合约" logs/monitor_1.log

# 查看跳过的重复合约
grep "跳过重复合约" logs/monitor_1.log

# 查看统计信息
grep "统计信息" logs/monitor_1.log
```

### 查看历史日志
```bash
# 查看指定日期的日志
cat logs/monitor_1.log.2024-01-01

# 查看最近3天的所有日志
cat logs/monitor_1.log.2024-01-0{1,2,3}

# 在历史日志中搜索特定内容
grep "发现新合约" logs/monitor_1.log.*

# 统计每天发现的合约数量
for log in logs/monitor_1.log.*; do
    echo "$(basename $log): $(grep -c "发现新合约" $log) 个合约"
done
```

### 日志文件管理
```bash
# 查看日志目录状态
ls -la logs/

# 查看日志文件大小
du -h logs/

# 手动清理超过7天的日志（通常不需要，系统会自动清理）
find logs/ -name "monitor_*.log.*" -mtime +7 -delete

# 查看去重数据库状态
ls -la *_sent_contracts.db
```

### 重启监控
```bash
# 如果使用systemd
sudo systemctl restart telegram-monitor

# 如果使用screen
screen -r telegram-monitor
# Ctrl+C 停止，然后重新运行 python monitor.py
```

## ⚠️ 注意事项

1. **网络稳定性**: 确保每台服务器到Telegram的网络连接稳定
2. **时区设置**: 建议所有服务器使用UTC时区
3. **防火墙**: 确保服务器可以访问Telegram API (443端口)
4. **资源监控**: 监控CPU和内存使用情况
5. **日志管理**: 系统自动管理日志文件，无需手动配置
6. **磁盘空间**: 确保有足够空间存储7天的日志文件
7. **权限设置**: 确保程序有权限创建 `logs/` 目录和写入日志文件

## 🚨 故障排除

### 常见问题

1. **连接失败**
   - 检查API_ID和API_HASH是否正确
   - 检查网络连接
   - 检查防火墙设置

2. **发送失败**
   - 检查SERVER_BOT_USERNAME是否正确
   - 确保Bot可以接收私聊消息
   - 检查网络连接

3. **权限错误**
   - 确保监控账号已加入所有目标群组
   - 检查账号是否被限制

### 监控脚本

可以创建简单的监控脚本检查所有服务器状态：

```bash
#!/bin/bash
# check_monitors.sh

servers=("server-a-ip" "server-b-ip" "server-c-ip" "server-d-ip")

for server in "${servers[@]}"; do
    echo "检查 $server..."
    ssh user@$server "ps aux | grep monitor.py | grep -v grep"
done
```

## 📈 性能优化

1. **资源配置**: 每个监控进程约需要100MB内存
2. **网络优化**: 使用CDN或代理优化到Telegram的连接
3. **负载均衡**: 根据群组活跃度调整分配

这样的分布式部署可以确保：
- ✅ 避免单点故障
- ✅ 分散API请求压力
- ✅ 提高网络稳定性
- ✅ 实现分布式监控
