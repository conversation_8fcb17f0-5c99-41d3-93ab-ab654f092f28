@echo off
chcp 65001 >nul
title 群组分析工具
echo.
echo 🚀 正在启动群组分析工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Python环境
    echo.
    echo 💡 请先安装Python：
    echo    1. 访问 https://www.python.org/downloads/
    echo    2. 下载并安装Python 3.6或更高版本
    echo    3. 安装时勾选"Add Python to PATH"
    echo.
    pause
    exit /b 1
)

REM 运行Python程序
python "群组分析工具.py"

REM 如果程序异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错
    echo.
    echo 💡 可能的解决方案：
    echo    1. 确保Python已正确安装
    echo    2. 检查"群组分析工具.py"文件是否存在
    echo    3. 尝试右键以管理员身份运行此批处理文件
    echo.
    pause
)
