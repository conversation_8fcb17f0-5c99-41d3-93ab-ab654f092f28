@echo off
title Group Analyzer Tool
echo.
echo Starting Group Analysis Tool...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found
    echo.
    echo Please install Python first:
    echo    1. Visit https://www.python.org/downloads/
    echo    2. Download and install Python 3.6 or higher
    echo    3. Check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Run Python program
python "群组分析工具.py"

REM If program exits with error, show error message
if %errorlevel% neq 0 (
    echo.
    echo Program error occurred
    echo.
    echo Possible solutions:
    echo    1. Make sure Python is correctly installed
    echo    2. Check if the Python file exists
    echo    3. Try running as administrator
    echo.
    pause
)
