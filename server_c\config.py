#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器C配置 - 监控账号3
"""

# 监控账号3的API配置
API_ID = '********'
API_HASH = '7f2c3ad38cfbfd99c6f6ec946c16d44a'
SESSION_NAME = 'monitor_3_session'
ACCOUNT_ID = 'monitor_3'



# HTTP推送配置
PUSH_URL = 'http://*************:5000/push'  # 接收bot的HTTP接口

# 监控账号3负责的群组（31个）
MONITOR_GROUPS = [
    -*************,  # Solana Premium VIP Calls 💊
    -*************,  # Gambles 🎲 MadApes
    -*************,  # C<PERSON> Calls 🐊
    -*************,  # lynk's pump group
    -*************,  # TheCryptoWorldGems
    -*************,  # Ben Based - Gambles
    -*************,  # 💰ANIME GEMS💰
    -*************,  # 🦇 BATMAN GAMBLE 🎲
    -*************,  # Mr <PERSON><PERSON> Calls
    -*************,  # Savannah Wakanda ℗
    -*************,  # BATMAN GEMS 🦇
    -*************,  # 𝐂𝐫𝐲𝐩𝐭𝐨𝐬𝐩𝐚𝐫𝐫𝐨𝐰 𝐜𝐚𝐥𝐥
    -*************,  # Jessie T's Channel
    -*************,  # BlockChainBrothers Degen {BCB}
    -*************,  # trench cabal
    -*************,  # Maestro Gamble
    -*************,  # Luigis Rugs and Moonshots Gamble Channel
    -1002227052221,  # Shizzlit pamps
    -1001296044889,  # Minty's Mooners #HOLD4GOLD
    -1002192337225,  # THANOS GEMS CALLS
    -1002353208529,  # TUBOR DEGEN CALLS
    -1001844099826,  # NAUGHTY APES🐵🐵
    -1001972446537,  # Predator Calls
    -1002178826499,  # Milagros Degen Calls (real)
    -1002015715777,  # Mitch 🎲 Gambles
    -1002187518673,  # DONALD CALLS
    -1002279724969,  # Sir_Williams Call
    -1002282383944,  # NTY Calls Meme Gamble
    -1002375572763,  # AUTO 007
    -1001956578733,  # Meme Space
    -1001661323654,  # Mrcryptgamble
]

# 服务器信息（用于标识）
SERVER_ID = 'server_c'
