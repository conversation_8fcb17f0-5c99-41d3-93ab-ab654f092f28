from flask import Flask, request
import requests
import time
import json
import os
import re
import threading
import logging
import sqlite3
import schedule
from datetime import datetime, timedelta
from logging.handlers import TimedRotatingFileHandler

app = Flask(__name__)

BOT_TOKEN = '7804905723:AAGsISnTldMzQWiX9Wk7j_WUqO7ckeMvjwI'  # 你的TG Bot Token
CHAT_ID = '6051982046'  # 系统提醒的私聊ID
GROUP_CHAT_ID = '-1002577888409'  # 合约推送的群组ID

def setup_logging():
    """设置日志 - 按天分割"""
    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建logger
    logger = logging.getLogger('receiver_bot')
    logger.setLevel(logging.INFO)

    # 清除已有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 按天分割的文件日志
    log_filename = os.path.join(log_dir, 'receiver_bot.log')
    file_handler = TimedRotatingFileHandler(
        filename=log_filename,
        when='midnight',
        interval=1,
        backupCount=7,  # 保留7天的日志
        encoding='utf-8',
        delay=False,
        utc=False
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    return logger

# 初始化日志
logger = setup_logging()

# 数据库配置
DB_PATH = 'contracts.db'
TIME_WINDOW = 24 * 60 * 60  # 24小时窗口，单位秒
PUSH_THRESHOLD = 5  # 推送阈值：5次
STORAGE_DAYS = 3  # 推送记录保存3天

# 添加最后接收时间记录
last_receive_time = time.time()
last_alert_time = 0  # 记录上次提醒时间，避免重复提醒

def init_database():
    """初始化数据库"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 创建合约推送记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contract_pushes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_address TEXT NOT NULL,
                monitor_id TEXT NOT NULL,
                group_id TEXT NOT NULL,
                group_name TEXT NOT NULL,
                push_time REAL NOT NULL,
                msg_time REAL NOT NULL
            )
        ''')

        # 创建已推送合约表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pushed_contracts (
                contract_address TEXT PRIMARY KEY,
                first_push_time REAL NOT NULL,
                push_count INTEGER NOT NULL,
                pushed_at REAL NOT NULL,
                reason TEXT NOT NULL
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_contract_pushes_contract ON contract_pushes(contract_address)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_contract_pushes_time ON contract_pushes(push_time)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pushed_contracts_time ON pushed_contracts(pushed_at)')

        conn.commit()
        conn.close()

        logger.info(f"✅ 数据库初始化完成: {DB_PATH}")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")

def cleanup_old_data():
    """清理过期数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 清理3天前的推送记录
        cutoff_time = time.time() - (STORAGE_DAYS * 24 * 60 * 60)

        cursor.execute('DELETE FROM pushed_contracts WHERE pushed_at < ?', (cutoff_time,))
        deleted_pushed = cursor.rowcount

        cursor.execute('DELETE FROM contract_pushes WHERE push_time < ?', (cutoff_time,))
        deleted_pushes = cursor.rowcount

        conn.commit()
        conn.close()

        if deleted_pushed > 0 or deleted_pushes > 0:
            logger.info(f"🧹 清理过期数据: 推送记录 {deleted_pushed} 条, 推送详情 {deleted_pushes} 条")

    except Exception as e:
        logger.error(f"❌ 清理数据失败: {e}")

def add_contract_push(contract_address, monitor_id, group_id, group_name, msg_time):
    """添加合约推送记录"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        current_time = time.time()
        cursor.execute('''
            INSERT INTO contract_pushes
            (contract_address, monitor_id, group_id, group_name, push_time, msg_time)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (contract_address, monitor_id, group_id, group_name, current_time, msg_time))

        conn.commit()
        conn.close()

    except Exception as e:
        logger.error(f"❌ 添加推送记录失败: {e}")

def get_contract_push_count(contract_address):
    """获取合约在24小时内的推送次数"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 24小时前的时间戳
        cutoff_time = time.time() - TIME_WINDOW

        cursor.execute('''
            SELECT COUNT(*), MIN(push_time) as first_time
            FROM contract_pushes
            WHERE contract_address = ? AND push_time >= ?
        ''', (contract_address, cutoff_time))

        result = cursor.fetchone()
        conn.close()

        if result:
            count, first_time = result
            return count or 0, first_time
        return 0, None

    except Exception as e:
        logger.error(f"❌ 获取推送次数失败: {e}")
        return 0, None

def is_contract_pushed(contract_address):
    """检查合约是否已推送过"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT 1 FROM pushed_contracts
            WHERE contract_address = ?
        ''', (contract_address,))

        result = cursor.fetchone()
        conn.close()

        return result is not None

    except Exception as e:
        logger.error(f"❌ 检查推送状态失败: {e}")
        return False

def mark_contract_pushed(contract_address, push_count, reason):
    """标记合约已推送"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 获取首次推送时间
        cursor.execute('''
            SELECT MIN(push_time) FROM contract_pushes
            WHERE contract_address = ?
        ''', (contract_address,))

        result = cursor.fetchone()
        first_time = result[0] if result and result[0] else time.time()

        cursor.execute('''
            INSERT OR REPLACE INTO pushed_contracts
            (contract_address, first_push_time, push_count, pushed_at, reason)
            VALUES (?, ?, ?, ?, ?)
        ''', (contract_address, first_time, push_count, time.time(), reason))

        conn.commit()
        conn.close()

    except Exception as e:
        logger.error(f"❌ 标记推送失败: {e}")

def send_to_telegram(text, chat_id=None):
    """发送消息到Telegram"""
    if chat_id is None:
        chat_id = CHAT_ID  # 默认发送到私聊（系统提醒）

    url = f'https://api.telegram.org/bot{BOT_TOKEN}/sendMessage'
    data = {'chat_id': chat_id, 'text': text, 'parse_mode': 'HTML'}
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info(f"✅ 消息发送成功到 {chat_id}")
        else:
            logger.error(f"❌ 消息发送失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f'❌ 发送到TG失败: {e}')

def send_contract_to_group(text):
    """发送合约信息到群组"""
    send_to_telegram(text, GROUP_CHAT_ID)

def send_alert_to_private(text):
    """发送系统提醒到私聊"""
    send_to_telegram(text, CHAT_ID)

def get_daily_pushed_contracts():
    """获取过去24小时内推送的合约详情"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 24小时前的时间戳
        cutoff_time = time.time() - (24 * 60 * 60)

        # 获取推送的合约信息
        cursor.execute('''
            SELECT contract_address, first_push_time, push_count, pushed_at, reason
            FROM pushed_contracts
            WHERE pushed_at >= ?
            ORDER BY pushed_at DESC
        ''', (cutoff_time,))

        pushed_contracts = cursor.fetchall()

        # 为每个合约获取触发推送的群组信息
        contract_details = []
        for contract_address, first_push_time, push_count, pushed_at, reason in pushed_contracts:
            # 获取该合约的所有推送记录
            cursor.execute('''
                SELECT DISTINCT monitor_id, group_id, group_name, push_time
                FROM contract_pushes
                WHERE contract_address = ? AND push_time >= ?
                ORDER BY push_time ASC
            ''', (contract_address, cutoff_time))

            push_records = cursor.fetchall()

            # 计算监控时间
            if first_push_time and pushed_at:
                monitor_duration = pushed_at - first_push_time
                if monitor_duration < 60:
                    time_str = f"{int(monitor_duration)}秒"
                elif monitor_duration < 3600:
                    minutes = int(monitor_duration / 60)
                    time_str = f"{minutes}分钟"
                else:
                    hours = int(monitor_duration / 3600)
                    minutes = int((monitor_duration % 3600) / 60)
                    if minutes > 0:
                        time_str = f"{hours}小时{minutes}分钟"
                    else:
                        time_str = f"{hours}小时"
            else:
                time_str = "未知"

            contract_details.append({
                'contract_address': contract_address,
                'push_count': push_count,
                'monitor_time': time_str,
                'pushed_at': datetime.fromtimestamp(pushed_at).strftime('%Y-%m-%d %H:%M:%S'),
                'reason': reason,
                'push_records': push_records
            })

        conn.close()
        return contract_details

    except Exception as e:
        logger.error(f"❌ 获取每日推送合约失败: {e}")
        return []

def create_daily_report():
    """创建每日推送报告"""
    try:
        # 创建报告目录
        report_dir = 'daily_reports'
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)

        # 获取昨天的日期
        yesterday = datetime.now() - timedelta(days=1)
        date_str = yesterday.strftime('%Y-%m-%d')

        # 获取推送的合约数据
        contracts = get_daily_pushed_contracts()

        if not contracts:
            logger.info(f"📋 {date_str} 无推送合约，跳过报告生成")
            return

        # 生成报告文件名
        report_filename = f"pushed_contracts_{date_str}.txt"
        report_path = os.path.join(report_dir, report_filename)

        # 生成报告内容
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"📊 每日推送合约报告\n")
            f.write(f"📅 日期: {date_str}\n")
            f.write(f"📈 总推送数量: {len(contracts)} 个合约\n")
            f.write(f"🕐 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")

            for i, contract in enumerate(contracts, 1):
                f.write(f"【合约 {i}】\n")
                f.write(f"💎 合约地址: {contract['contract_address']}\n")
                f.write(f"⏱️ 监控时间: {contract['monitor_time']}\n")
                f.write(f"📊 推送次数: {contract['push_count']} 次\n")
                f.write(f"🚀 推送时间: {contract['pushed_at']}\n")
                f.write(f"🔥 触发原因: {contract['reason']}\n")
                f.write(f"📢 涉及群组:\n")

                # 按监控bot分组显示群组
                bot_groups = {}
                for monitor_id, group_id, group_name, push_time in contract['push_records']:
                    if monitor_id not in bot_groups:
                        bot_groups[monitor_id] = []
                    bot_groups[monitor_id].append({
                        'group_id': group_id,
                        'group_name': group_name,
                        'push_time': datetime.fromtimestamp(push_time).strftime('%H:%M:%S')
                    })

                for bot_id, groups in bot_groups.items():
                    f.write(f"   🤖 {bot_id}:\n")
                    for group in groups:
                        f.write(f"      • {group['push_time']} - {group['group_name']} ({group['group_id']})\n")

                f.write(f"   🔗 GMGN: https://gmgn.ai/sol/token/{contract['contract_address']}\n")
                f.write(f"   🔗 AXIOM: https://axiom.trade/meme/{contract['contract_address']}\n")
                f.write("\n" + "-" * 60 + "\n\n")

        logger.info(f"✅ 每日报告已生成: {report_path}")

        # 发送报告生成通知到私聊
        notification = f"📊 每日推送报告已生成\n📅 日期: {date_str}\n📈 推送合约: {len(contracts)} 个\n📁 文件: {report_filename}"
        send_alert_to_private(notification)

    except Exception as e:
        logger.error(f"❌ 生成每日报告失败: {e}")

def schedule_daily_tasks():
    """安排每日任务"""
    # 每天0点生成报告
    schedule.every().day.at("00:00").do(create_daily_report)

    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分钟检查一次

def check_inactivity():
    """定时检查是否长时间没有收到信息"""
    global last_receive_time, last_alert_time
    
    while True:
        try:
            current_time = time.time()
            time_since_last = current_time - last_receive_time
            minutes_since_last = int(time_since_last / 60)
            
            # 检查是否需要发送提醒
            if minutes_since_last >= 10:
                # 避免重复提醒，只在特定时间点提醒
                if minutes_since_last == 10 or minutes_since_last == 20 or minutes_since_last == 30:
                    if current_time - last_alert_time > 60:  # 至少间隔1分钟再提醒
                        alert_msg = f"⚠️ 服务器已 {minutes_since_last} 分钟未收到新消息，请检查监控程序是否正常运行！"
                        send_alert_to_private(alert_msg)
                        last_alert_time = current_time
                        logger.info(f"发送 {minutes_since_last} 分钟无消息提醒")

                # 超过30分钟后，每30分钟提醒一次
                elif minutes_since_last > 30 and minutes_since_last % 30 == 0:
                    if current_time - last_alert_time > 60:
                        alert_msg = f"⚠️ 服务器已 {minutes_since_last} 分钟未收到新消息，请检查监控程序是否正常运行！"
                        send_alert_to_private(alert_msg)
                        last_alert_time = current_time
                        logger.info(f"发送 {minutes_since_last} 分钟无消息提醒")

        except Exception as e:
            logger.error(f"定时检查出错: {e}")
        
        # 每30秒检查一次
        time.sleep(30)

def parse_message(msg):
    """解析消息，提取合约地址、监控bot和群组信息"""
    contract = None
    monitor_id = "unknown"
    group_id = "unknown"
    group_name = "unknown"

    # 提取合约地址
    if 'Ca::' in msg:
        contract = msg.split('Ca::')[-1].strip()

    # 提取监控bot信息
    monitor_match = re.search(r'Monitor:\s*(\w+)', msg)
    if monitor_match:
        monitor_id = monitor_match.group(1)

    # 提取群组信息
    group_match = re.search(r'Group:\s*(-?\d+)', msg)
    if group_match:
        group_id = group_match.group(1)

    # 提取群组名称 - 改进正则表达式以处理各种格式
    group_name_match = re.search(r'GroupName:\s*(.+?)\s+Monitor:', msg)
    if group_name_match:
        group_name = group_name_match.group(1).strip()
        # 如果群组名称是 "群组_数字" 格式，显示为 unknown
        if re.match(r'^群组_-?\d+$', group_name):
            group_name = "unknown"

    return contract, monitor_id, group_id, group_name

def extract_msg_time(msg):
    """从消息中提取时间信息，返回时间戳"""
    # 匹配格式 Time: 06-26 11:56:30 或 Time: 06-26 11:56
    m = re.search(r'Time:\s*(\d{2})-(\d{2}) (\d{2}):(\d{2})(?::(\d{2}))?', msg)
    if m:
        month, day, hour, minute = map(int, m.groups()[:4])
        second = int(m.group(5)) if m.group(5) else 0

        # 假设是当前年份
        year = time.localtime().tm_year
        # 创建时间戳
        try:
            timestamp = time.mktime((year, month, day, hour, minute, second, 0, 0, -1))

            # 检查时间戳是否合理（不能是未来时间，也不能太早）
            current_time = time.time()
            if timestamp > current_time:
                # 如果是未来时间，可能是去年的数据，尝试减一年
                timestamp = time.mktime((year-1, month, day, hour, minute, second, 0, 0, -1))
                if timestamp > current_time:
                    # 如果还是未来时间，使用当前时间
                    logger.warning(f"消息时间 {month}-{day} {hour}:{minute}:{second} 是未来时间，使用当前时间")
                    return current_time

            return timestamp
        except Exception as e:
            logger.error(f"时间解析失败: {e}")

    # 如果解析失败，返回当前时间
    logger.warning(f"无法解析消息时间，使用当前时间: {msg}")
    return time.time()

def check_push_conditions(contract_address):
    """检查是否满足推送条件"""
    # 检查是否已推送过
    if is_contract_pushed(contract_address):
        return False, "已推送过"

    # 获取24小时内的推送次数
    push_count, first_time = get_contract_push_count(contract_address)

    if push_count == 0:
        return False, "新合约"

    # 检查是否达到推送阈值
    if push_count >= PUSH_THRESHOLD:
        return True, f"24小时内{push_count}次推送"

    # 检查是否超过24小时窗口
    if first_time and (time.time() - first_time) > TIME_WINDOW:
        return False, f"超过24小时窗口(共{push_count}次)"

    return False, f"未达到推送条件({push_count}/{PUSH_THRESHOLD}次)"

@app.route('/push', methods=['POST'])
def push():
    global last_receive_time
    
    try:
        # 更新最后接收时间
        last_receive_time = time.time()
        
        data = request.json
        if not data:
            logger.warning("收到空数据")
            return 'ok', 200

        msg = data.get('msg', '')
        if not msg:
            logger.warning("收到空消息")
            return 'ok', 200

        # 解析消息内容
        contract, monitor_id, group_id, group_name = parse_message(msg)
        if not contract:
            logger.warning(f"收到无合约推送: {msg}")
            return 'ok', 200

        # 提取消息时间
        msg_time = extract_msg_time(msg)
        current_time = time.time()

        # 添加推送记录到数据库
        add_contract_push(contract, monitor_id, group_id, group_name, msg_time)

        # 获取当前推送次数
        push_count, first_time = get_contract_push_count(contract)

        # 显示接收信息
        if push_count == 1:
            logger.info(f"🆕 新合约: Bot={monitor_id} 群组={group_name}({group_id}) 合约={contract} 第1次推送")
        else:
            logger.info(f"📈 累计推送: Bot={monitor_id} 群组={group_name}({group_id}) 合约={contract} 第{push_count}次")

        # 检查是否满足推送条件
        should_push, reason = check_push_conditions(contract)

        # 如果满足推送条件，发送到Telegram
        if should_push:
            # 计算监控时间
            push_count, first_time = get_contract_push_count(contract)
            current_time = time.time()

            if first_time:
                monitor_duration = current_time - first_time

                # 转换为合适的时间单位
                if monitor_duration < 60:
                    time_str = f"{int(monitor_duration)}秒"
                elif monitor_duration < 3600:
                    minutes = int(monitor_duration / 60)
                    time_str = f"{minutes}分钟"
                else:
                    hours = int(monitor_duration / 3600)
                    minutes = int((monitor_duration % 3600) / 60)
                    if minutes > 0:
                        time_str = f"{hours}小时{minutes}分钟"
                    else:
                        time_str = f"{hours}小时"
            else:
                time_str = "未知"

            gmgn_url = f"https://gmgn.ai/sol/token/{contract}"
            axiom_url = f"https://axiom.trade/meme/{contract}"
            push_msg = (
                f"💎 Ca: <code>{contract}</code>\n"
                f"<a href='{gmgn_url}'>GMGN</a>  <a href='{axiom_url}'>AXIOM</a>\n"
                f"⏱️ 监控时间: {time_str}"
            )
            send_contract_to_group(push_msg)
            mark_contract_pushed(contract, push_count, reason)
            logger.info(f"🚀 推送成功到群组: Bot={monitor_id} 群组={group_name}({group_id}) 合约={contract} 监控时间={time_str} 原因={reason}")
        elif "超过24小时窗口" in reason:
            # 超过24小时窗口，标记为已推送（永不推送）
            mark_contract_pushed(contract, push_count, reason)
            logger.info(f"⏰ 窗口关闭: Bot={monitor_id} 群组={group_name}({group_id}) 合约={contract}")

        return 'ok', 200

    except Exception as e:
        logger.error(f"push函数出错: {e}")
        return 'error', 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    global last_receive_time
    current_time = time.time()
    time_since_last = current_time - last_receive_time
    minutes_since_last = int(time_since_last / 60)
    
    return {
        'status': 'ok',
        'last_receive_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_receive_time)),
        'minutes_since_last': minutes_since_last,
        'server_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))
    }

def cleanup_task():
    """定期清理过期数据"""
    while True:
        try:
            time.sleep(3600)  # 每小时清理一次
            cleanup_old_data()
        except Exception as e:
            logger.error(f"清理任务失败: {e}")

if __name__ == '__main__':
    logger.info("=" * 60)
    logger.info("服务器启动中...")

    # 初始化数据库
    init_database()

    # 清理过期数据
    cleanup_old_data()

    # 启动定时检查线程
    check_thread = threading.Thread(target=check_inactivity, daemon=True)
    check_thread.start()
    logger.info("✅ 定时检查线程已启动")

    # 启动清理任务线程
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("✅ 数据清理线程已启动")

    # 启动每日任务调度线程
    schedule_thread = threading.Thread(target=schedule_daily_tasks, daemon=True)
    schedule_thread.start()
    logger.info("✅ 每日任务调度线程已启动")

    logger.info("🔄 每30秒检查一次无消息状态")
    logger.info("📢 10/20/30分钟无消息时发送提醒")
    logger.info(f"📊 新推送逻辑: {PUSH_THRESHOLD}次推送触发，24小时窗口，推送记录保存{STORAGE_DAYS}天")
    logger.info("📋 每日0点自动生成推送报告")
    logger.info("📁 报告保存路径: ./daily_reports/")
    logger.info("=" * 60)

    app.run(host='0.0.0.0', port=5000)