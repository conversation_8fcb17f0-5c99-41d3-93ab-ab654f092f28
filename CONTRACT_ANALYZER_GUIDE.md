# 🔍 合约群组分析工具使用指南

## 📋 工具概述

这个工具可以帮助您分析一批合约地址在哪些Telegram群组中出现最频繁，找出最有价值的信号源群组。

## 🛠️ 工具版本

提供了3个版本的分析工具：

### 1. 命令行版本 (`contract_analyzer.py`)
- **适合**: 技术用户，批量处理
- **特点**: 功能完整，支持文件输入输出
- **使用**: `python contract_analyzer.py`

### 2. 图形界面版本 (`contract_analyzer_gui.py`)
- **适合**: 普通用户，傻瓜式操作
- **特点**: 可视化界面，操作简单
- **使用**: `python contract_analyzer_gui.py`

### 3. Web API版本 (集成在接收服务器中)
- **适合**: 程序调用，远程分析
- **特点**: HTTP接口，支持远程调用
- **使用**: POST请求到 `/analyze` 接口

## 🚀 快速开始

### 使用图形界面版本（推荐新手）

1. **启动工具**
   ```bash
   python contract_analyzer_gui.py
   ```

2. **输入合约地址**
   - 在文本框中输入合约地址，每行一个
   - 或点击"📁 从文件导入"从txt文件导入

3. **开始分析**
   - 点击"🔍 开始分析"按钮
   - 等待分析完成

4. **查看结果**
   - 结果会显示在下方的文本框中
   - 包含群组排名和统计信息

5. **保存报告**
   - 点击"💾 保存报告"保存详细的JSON报告

### 使用命令行版本

1. **启动工具**
   ```bash
   python contract_analyzer.py
   ```

2. **选择输入方式**
   - 选择1：手动输入合约地址
   - 选择2：从文件读取合约地址
   - 选择3：退出

3. **查看分析结果**
   - 工具会显示详细的分析报告
   - 可选择保存详细报告到JSON文件

## 📊 分析结果说明

### 基本统计信息
- **输入合约数量**: 您提供的合约地址总数
- **找到合约数量**: 在数据库中找到记录的合约数量
- **覆盖率**: 找到的合约占输入合约的百分比
- **涉及群组数量**: 这些合约涉及的群组总数
- **总推送次数**: 所有合约的推送次数总和

### 群组活跃度排名
按照总推送次数排序，显示：
- **群组名称**: Telegram群组的名称
- **群组ID**: 群组的唯一标识符
- **监控Bot**: 负责监控该群组的Bot编号
- **总推送次数**: 该群组推送您输入的合约的总次数
- **涉及合约数**: 该群组推送了您输入的多少个不同合约

### 未找到的合约
列出在数据库中没有记录的合约地址，可能原因：
- 合约地址错误
- 合约从未在监控的群组中出现过
- 合约出现时间超出数据保存期限

## 📁 输入格式

### 合约地址格式
```
7fV1mF7JGamCCSE1Tk4VYXVLuqKGHLSnLxEg5TKBbonk
4FkUfLMkGMHzUCCzzS9wnqyFaL6WqXmUTEBqJGpobonk
BNRMxRCaYWhKgx2PNcmwH2DAGcRQmdt2xgh79GULbonk
```

### 文件输入
创建一个txt文件，每行一个合约地址：
```
# contracts.txt
7fV1mF7JGamCCSE1Tk4VYXVLuqKGHLSnLxEg5TKBbonk
4FkUfLMkGMHzUCCzzS9wnqyFaL6WqXmUTEBqJGpobonk
BNRMxRCaYWhKgx2PNcmwH2DAGcRQmdt2xgh79GULbonk
```

## 🔧 Web API使用

### 请求格式
```bash
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "contracts": [
      "7fV1mF7JGamCCSE1Tk4VYXVLuqKGHLSnLxEg5TKBbonk",
      "4FkUfLMkGMHzUCCzzS9wnqyFaL6WqXmUTEBqJGpobonk"
    ]
  }'
```

### 响应格式
```json
{
  "success": true,
  "summary": {
    "total_input_contracts": 2,
    "found_contracts": 2,
    "coverage_rate": 100.0,
    "total_groups": 5,
    "total_pushes": 15
  },
  "group_ranking": [
    {
      "group_id": -1002380594298,
      "group_name": "🐋 Free Whale Signals 🐋",
      "monitor_id": "monitor_1",
      "total_pushes": 8,
      "unique_contracts": 2
    }
  ],
  "missing_contracts": []
}
```

## 💡 使用技巧

### 1. 批量分析
- 可以一次输入几十个甚至上百个合约地址
- 工具会自动去重和清理无效地址

### 2. 结果解读
- **高推送次数的群组** = 活跃的信号源
- **涉及合约数多的群组** = 覆盖面广的群组
- **覆盖率低** = 可能需要扩大监控范围

### 3. 定期分析
- 建议定期分析最近的热门合约
- 找出新兴的有价值群组
- 调整监控策略

## ⚠️ 注意事项

1. **数据库依赖**: 工具需要访问接收服务器的数据库文件
2. **数据时效**: 只能分析数据库中有记录的合约（通常是最近几天）
3. **权限要求**: 确保有读取数据库文件的权限
4. **内存使用**: 分析大量合约时可能占用较多内存

## 🔍 故障排除

### 常见问题

1. **数据库文件不存在**
   - 确保接收服务器已运行
   - 检查数据库路径是否正确
   - 确认有数据写入

2. **没有找到合约记录**
   - 检查合约地址是否正确
   - 确认合约是否在监控期间出现过
   - 检查数据保存期限设置

3. **界面无响应**
   - 分析大量合约时需要等待
   - 检查是否有错误提示
   - 重启工具重试

### 性能优化

- 单次分析建议不超过1000个合约
- 大批量分析可以分批进行
- 定期清理无用的分析报告文件

## 📈 实际应用场景

### 1. 信号源评估
输入最近一周的热门合约，找出最活跃的信号群组

### 2. 群组价值排名
分析特定类型合约（如meme币），评估不同群组的信号质量

### 3. 监控策略优化
根据分析结果调整监控群组的优先级和配置

### 4. 趋势分析
定期分析，观察群组活跃度的变化趋势

这个工具可以帮助您更好地理解和优化您的合约监控策略，找出最有价值的信号源！
