# 🔍 合约群组分析工具使用指南

## 📋 工具概述

这个工具可以帮助您分析一批合约地址在哪些Telegram群组中出现最频繁，找出最有价值的信号源群组。

## 🛠️ 工具版本

提供了3个版本的分析工具：

### 1. 每日报告分析器 (`daily_report_analyzer.py`) ⭐推荐
- **适合**: 所有用户，最实用的版本
- **特点**: 直接读取每日报告文件，自动分析群组活跃度
- **数据源**: 接收端每天凌晨自动生成的 `daily_reports` 文件
- **使用**: `python daily_report_analyzer.py`

### 2. 合约地址分析器 (`contract_analyzer_gui.py`)
- **适合**: 需要分析特定合约的用户
- **特点**: 手动输入合约地址，查看在哪些群组出现
- **数据源**: 直接查询数据库
- **使用**: `python contract_analyzer_gui.py`

### 3. 命令行版本 (`contract_analyzer.py`)
- **适合**: 技术用户，批量处理
- **特点**: 功能完整，支持文件输入输出
- **使用**: `python contract_analyzer.py`

## 🚀 快速开始

### 使用每日报告分析器（最推荐）

1. **启动工具**
   ```bash
   python daily_report_analyzer.py
   ```

2. **设置分析参数**
   - 选择分析最近几天的数据（1-30天）
   - 确认报告目录路径（默认：daily_reports）

3. **开始分析**
   - 点击"🔍 开始分析"按钮
   - 工具会自动读取每日报告文件

4. **查看结果**
   - 显示群组活跃度排名
   - 包含推送次数、涉及合约数、活跃天数等统计

5. **保存结果**
   - 点击"💾 保存结果"保存JSON格式的详细报告

### 使用合约地址分析器

1. **启动工具**
   ```bash
   python contract_analyzer_gui.py
   ```

2. **输入合约地址**
   - 在文本框中输入合约地址，每行一个
   - 或点击"📁 从文件导入"从txt文件导入

3. **开始分析**
   - 点击"🔍 开始分析"按钮
   - 等待分析完成

4. **查看结果**
   - 结果会显示在下方的文本框中
   - 包含群组排名和统计信息

### 使用命令行版本

1. **启动工具**
   ```bash
   python contract_analyzer.py
   ```

2. **选择输入方式**
   - 选择1：手动输入合约地址
   - 选择2：从文件读取合约地址
   - 选择3：退出

3. **查看分析结果**
   - 工具会显示详细的分析报告
   - 可选择保存详细报告到JSON文件

## 📊 分析结果说明

### 基本统计信息
- **输入合约数量**: 您提供的合约地址总数
- **找到合约数量**: 在数据库中找到记录的合约数量
- **覆盖率**: 找到的合约占输入合约的百分比
- **涉及群组数量**: 这些合约涉及的群组总数
- **总推送次数**: 所有合约的推送次数总和

### 群组活跃度排名
按照总推送次数排序，显示：
- **群组名称**: Telegram群组的名称
- **群组ID**: 群组的唯一标识符
- **监控Bot**: 负责监控该群组的Bot编号
- **总推送次数**: 该群组推送您输入的合约的总次数
- **涉及合约数**: 该群组推送了您输入的多少个不同合约

### 未找到的合约
列出在数据库中没有记录的合约地址，可能原因：
- 合约地址错误
- 合约从未在监控的群组中出现过
- 合约出现时间超出数据保存期限

## 📁 输入格式

### 合约地址格式
```
7fV1mF7JGamCCSE1Tk4VYXVLuqKGHLSnLxEg5TKBbonk
4FkUfLMkGMHzUCCzzS9wnqyFaL6WqXmUTEBqJGpobonk
BNRMxRCaYWhKgx2PNcmwH2DAGcRQmdt2xgh79GULbonk
```

### 文件输入
创建一个txt文件，每行一个合约地址：
```
# contracts.txt
7fV1mF7JGamCCSE1Tk4VYXVLuqKGHLSnLxEg5TKBbonk
4FkUfLMkGMHzUCCzzS9wnqyFaL6WqXmUTEBqJGpobonk
BNRMxRCaYWhKgx2PNcmwH2DAGcRQmdt2xgh79GULbonk
```

## 📊 每日报告分析器详细说明

### 数据源
- **文件位置**: `daily_reports/` 文件夹
- **文件格式**: `pushed_contracts_YYYY-MM-DD.txt`
- **生成时间**: 每天凌晨0点自动生成
- **数据内容**: 前一天推送的所有合约详情

### 分析维度
- **群组活跃度**: 按推送次数排序
- **合约覆盖**: 每个群组涉及的合约数量
- **时间跨度**: 可选择分析最近1-30天
- **监控Bot**: 显示负责监控该群组的Bot

### 结果指标
- **总推送次数**: 该群组推送合约的总次数
- **涉及合约数**: 该群组推送的不同合约数量
- **活跃天数**: 该群组在分析期间有推送的天数
- **日均推送**: 平均每天的推送次数

## 💡 使用技巧

### 1. 批量分析
- 可以一次输入几十个甚至上百个合约地址
- 工具会自动去重和清理无效地址

### 2. 结果解读
- **高推送次数的群组** = 活跃的信号源
- **涉及合约数多的群组** = 覆盖面广的群组
- **覆盖率低** = 可能需要扩大监控范围

### 3. 定期分析
- 建议定期分析最近的热门合约
- 找出新兴的有价值群组
- 调整监控策略

## ⚠️ 注意事项

1. **数据库依赖**: 工具需要访问接收服务器的数据库文件
2. **数据时效**: 只能分析数据库中有记录的合约（通常是最近几天）
3. **权限要求**: 确保有读取数据库文件的权限
4. **内存使用**: 分析大量合约时可能占用较多内存

## 🔍 故障排除

### 常见问题

1. **数据库文件不存在**
   - 确保接收服务器已运行
   - 检查数据库路径是否正确
   - 确认有数据写入

2. **没有找到合约记录**
   - 检查合约地址是否正确
   - 确认合约是否在监控期间出现过
   - 检查数据保存期限设置

3. **界面无响应**
   - 分析大量合约时需要等待
   - 检查是否有错误提示
   - 重启工具重试

### 性能优化

- 单次分析建议不超过1000个合约
- 大批量分析可以分批进行
- 定期清理无用的分析报告文件

## 📈 实际应用场景

### 1. 信号源评估
输入最近一周的热门合约，找出最活跃的信号群组

### 2. 群组价值排名
分析特定类型合约（如meme币），评估不同群组的信号质量

### 3. 监控策略优化
根据分析结果调整监控群组的优先级和配置

### 4. 趋势分析
定期分析，观察群组活跃度的变化趋势

这个工具可以帮助您更好地理解和优化您的合约监控策略，找出最有价值的信号源！
