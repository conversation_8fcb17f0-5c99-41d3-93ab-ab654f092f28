#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合约群组分析工具 - 图形界面版本
功能：分析输入的合约地址在哪些群组中出现最频繁
使用方法：python contract_analyzer_gui.py
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import sqlite3
import os
import json
from datetime import datetime
from collections import defaultdict

class ContractAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("合约群组分析工具")
        self.root.geometry("800x600")
        
        self.db_path = 'sever-bot/contracts.db'
        
        self.setup_ui()
        self.check_database()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 合约群组分析工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="输入合约地址", padding="10")
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 合约输入框
        ttk.Label(input_frame, text="请输入合约地址 (每行一个):").grid(row=0, column=0, sticky=tk.W)
        
        self.contract_text = scrolledtext.ScrolledText(input_frame, height=8, width=70)
        self.contract_text.grid(row=1, column=0, columnspan=3, pady=(5, 10), sticky=(tk.W, tk.E))
        
        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="📁 从文件导入", command=self.load_from_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔍 开始分析", command=self.analyze_contracts).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🗑️ 清空", command=self.clear_input).pack(side=tk.LEFT, padx=(0, 10))
        
        # 结果区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 结果显示
        self.result_text = scrolledtext.ScrolledText(result_frame, height=20, width=70)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 保存按钮
        ttk.Button(result_frame, text="💾 保存报告", command=self.save_report).grid(row=1, column=0, pady=(10, 0))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        input_frame.columnconfigure(0, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        self.results = None
    
    def check_database(self):
        """检查数据库是否存在"""
        if not os.path.exists(self.db_path):
            messagebox.showwarning("数据库未找到", 
                                 f"数据库文件不存在: {self.db_path}\n"
                                 "请确保接收服务器已运行并有数据")
    
    def load_from_file(self):
        """从文件加载合约地址"""
        filename = filedialog.askopenfilename(
            title="选择合约地址文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.contract_text.delete(1.0, tk.END)
                self.contract_text.insert(1.0, content)
                messagebox.showinfo("成功", f"已从文件加载合约地址")
            except Exception as e:
                messagebox.showerror("错误", f"读取文件失败: {e}")
    
    def clear_input(self):
        """清空输入"""
        self.contract_text.delete(1.0, tk.END)
        self.result_text.delete(1.0, tk.END)
        self.results = None
    
    def analyze_contracts(self):
        """分析合约"""
        if not os.path.exists(self.db_path):
            messagebox.showerror("错误", "数据库文件不存在，请检查接收服务器是否运行")
            return
        
        # 获取输入的合约地址
        content = self.contract_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "请输入合约地址")
            return
        
        contracts = [line.strip() for line in content.split('\n') if line.strip()]
        if not contracts:
            messagebox.showwarning("警告", "没有有效的合约地址")
            return
        
        # 显示分析进度
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"🔍 正在分析 {len(contracts)} 个合约...\n")
        self.root.update()
        
        try:
            self.results = self.perform_analysis(contracts)
            self.display_results()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {e}")
    
    def perform_analysis(self, contracts):
        """执行分析"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询合约推送记录
        placeholders = ','.join(['?' for _ in contracts])
        query = f'''
            SELECT 
                group_id,
                group_name,
                monitor_id,
                contract_address,
                COUNT(*) as push_count,
                MIN(push_time) as first_seen,
                MAX(push_time) as last_seen
            FROM contract_pushes 
            WHERE contract_address IN ({placeholders})
            GROUP BY group_id, group_name, monitor_id, contract_address
            ORDER BY group_id, push_count DESC
        '''
        
        cursor.execute(query, contracts)
        results = cursor.fetchall()
        
        if not results:
            conn.close()
            return None
        
        # 统计分析
        group_stats = defaultdict(lambda: {
            'group_name': '',
            'monitor_id': '',
            'total_pushes': 0,
            'unique_contracts': set(),
            'contracts_detail': []
        })
        
        contract_stats = defaultdict(lambda: {
            'total_groups': 0,
            'total_pushes': 0,
            'groups': []
        })
        
        for row in results:
            group_id, group_name, monitor_id, contract_address, push_count, first_seen, last_seen = row
            
            # 群组统计
            group_stats[group_id]['group_name'] = group_name
            group_stats[group_id]['monitor_id'] = monitor_id
            group_stats[group_id]['total_pushes'] += push_count
            group_stats[group_id]['unique_contracts'].add(contract_address)
            
            # 合约统计
            contract_stats[contract_address]['total_groups'] += 1
            contract_stats[contract_address]['total_pushes'] += push_count
        
        conn.close()
        
        # 转换为排序列表
        group_ranking = []
        for group_id, stats in group_stats.items():
            group_ranking.append({
                'group_id': group_id,
                'group_name': stats['group_name'],
                'monitor_id': stats['monitor_id'],
                'total_pushes': stats['total_pushes'],
                'unique_contracts': len(stats['unique_contracts'])
            })
        
        # 按总推送次数排序
        group_ranking.sort(key=lambda x: x['total_pushes'], reverse=True)
        
        # 计算统计信息
        found_contracts = len(contract_stats)
        coverage_rate = (found_contracts / len(contracts) * 100) if contracts else 0
        missing_contracts = [c for c in contracts if c not in contract_stats]
        
        return {
            'summary': {
                'total_input_contracts': len(contracts),
                'found_contracts': found_contracts,
                'coverage_rate': round(coverage_rate, 2),
                'total_groups': len(group_stats),
                'total_pushes': sum(stats['total_pushes'] for stats in group_stats.values())
            },
            'group_ranking': group_ranking,
            'missing_contracts': missing_contracts
        }
    
    def display_results(self):
        """显示分析结果"""
        self.result_text.delete(1.0, tk.END)
        
        if not self.results:
            self.result_text.insert(tk.END, "❌ 在数据库中没有找到这些合约的记录\n")
            return
        
        summary = self.results['summary']
        group_ranking = self.results['group_ranking']
        missing_contracts = self.results['missing_contracts']
        
        # 基本统计
        self.result_text.insert(tk.END, "📊 合约群组分析报告\n")
        self.result_text.insert(tk.END, "="*60 + "\n\n")
        
        self.result_text.insert(tk.END, f"📈 输入合约数量: {summary['total_input_contracts']}\n")
        self.result_text.insert(tk.END, f"✅ 找到合约数量: {summary['found_contracts']}\n")
        self.result_text.insert(tk.END, f"📊 覆盖率: {summary['coverage_rate']}%\n")
        self.result_text.insert(tk.END, f"🏠 涉及群组数量: {summary['total_groups']}\n")
        self.result_text.insert(tk.END, f"📤 总推送次数: {summary['total_pushes']}\n\n")
        
        # 群组排名
        self.result_text.insert(tk.END, "🏆 群组活跃度排名:\n")
        self.result_text.insert(tk.END, "-"*60 + "\n")
        
        for i, group in enumerate(group_ranking[:15], 1):  # 显示前15名
            self.result_text.insert(tk.END, f"#{i:2d} 📢 {group['group_name']}\n")
            self.result_text.insert(tk.END, f"     📍 群组ID: {group['group_id']}\n")
            self.result_text.insert(tk.END, f"     🤖 监控Bot: {group['monitor_id']}\n")
            self.result_text.insert(tk.END, f"     📊 总推送: {group['total_pushes']} 次\n")
            self.result_text.insert(tk.END, f"     💎 涉及合约: {group['unique_contracts']} 个\n\n")
        
        # 未找到的合约
        if missing_contracts:
            self.result_text.insert(tk.END, f"\n❌ 未找到的合约 ({len(missing_contracts)}个):\n")
            for contract in missing_contracts[:10]:  # 只显示前10个
                self.result_text.insert(tk.END, f"   {contract}\n")
            if len(missing_contracts) > 10:
                self.result_text.insert(tk.END, f"   ... 还有 {len(missing_contracts) - 10} 个\n")
    
    def save_report(self):
        """保存报告"""
        if not self.results:
            messagebox.showwarning("警告", "没有分析结果可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = filedialog.asksaveasfilename(
            title="保存分析报告",
            defaultextension=".json",
            initialvalue=f"contract_analysis_{timestamp}.json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"报告已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

def main():
    root = tk.Tk()
    app = ContractAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
