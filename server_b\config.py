#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器B配置 - 监控账号2
"""

# 监控账号2的API配置
API_ID = '********'
API_HASH = '01b84f3fdb0e0b7be3e457bce1b78da1'
SESSION_NAME = 'monitor_2_session'
ACCOUNT_ID = 'monitor_2'



# HTTP推送配置
PUSH_URL = 'http://75.127.12.103:5000/push'  # 接收bot的HTTP接口

# 监控账号2负责的群组（33个）
MONITOR_GROUPS = [
    -*************,  # 98K🎯
    -*************,  # GentleCat Calls
    -*************,  # THE DEGEN BOYS
    -*************,  # Pic Gems Calls
    -*************,  # A<PERSON><PERSON><PERSON> Degen Ape
    -*************,  # ChapoTheCrypto
    -*************,  # <PERSON><PERSON><PERSON> Calls - Solana
    -*************,  # BATMAN GAMBLE 🦇
    -*************,  # Zion.ETH Gems
    -*************,  # Quartz Public Hub
    -*************,  # Bruiser's Gambles🍜⛩️
    -*************,  # Gubbins Calls
    -*************,  # Mini Degen Calls
    -*************,  # Matt Furie calls
    -*************,  # Wilderness Gambles
    -*************,  # Mrpunk Cooks
    -*************,  # <PERSON>'s SOL, ETH & BSC Alpha Calls
    -*************,  # @Alfa1_Calls
    -*************,  # ᴛᴇsʟᴀ ᴄᴀʟʟs ᴏғғɪᴄɪᴀʟ ™
    -1002017857449,  # PEPE CALLS
    -1002362597228,  # Parker gambles 1000x 👾
    -1001227016438,  # Original KobeCalls
    -1001700527141,  # coragpt.eth and sol
    -1002133442854,  # Apollo Calls 🎰
    -1002356000950,  # Memeseus Maximus (Public)
    -1002480004728,  # sensei's dojo
    -1001891895394,  # The Kall 🚨
    -1001922572023,  # Kai's Calls
    -1002140481133,  # Kai's Gambles
    -1002000078706,  # NIKOLA DEGEN CALLS
    -1002058968393,  # The Casino
    -1001850324299,  # Printing Shitcoin
    -1001729018335,  # CS Ducks _Move Gambles Call
]

# 服务器信息（用于标识）
SERVER_ID = 'server_b'
