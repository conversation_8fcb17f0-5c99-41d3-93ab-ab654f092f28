#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器D配置 - 监控账号4
"""

# 监控账号4的API配置
API_ID = '********'
API_HASH = 'f2e0be13dd0765156e06387c657f5238'
SESSION_NAME = 'monitor_4_session'
ACCOUNT_ID = 'monitor_4'



# HTTP推送配置
PUSH_URL = 'http://75.127.12.103:5000/push'  # 接收bot的HTTP接口

# 监控账号4负责的群组（27个）
MONITOR_GROUPS = [
    -*************,  # 海豚🐬24小时扫链开麦沟通信息
    -*************,  # Wizzy's Trades
    -*************,  # 🇨🇳币道 🟢ETH/BSC/SOL @BiDaoBi 📞🎙
    -*************,  # OpalGems | Channel
    -*************,  # Pumpfun Ultimate Alert
    -*************,  # JohhWick MemeSafari
    -*************,  # <PERSON><PERSON> PompeLeeShuz
    -*************,  # <PERSON><PERSON><PERSON> Calls🐻
    -*************,  # Cas Gem (EL404)
    -*************,  # POSEIDON DEGEN CALLS
    -*************,  # To Millions - Gambles🐋 赌博
    -*************,  # Gems Mine(Gamble)
    -*************,  # CryptoMiners | News
    -*************,  # Rich Duck Calls
    -*************,  # Lukas Ape Degens
    -*************,  # FRENS GEMS 💎
    -*************,  # HERCULES DEGEN CALLS
    -*************,  # Doraemon of X100 CALLS
    -1001827145579,  # Crypτoshi's Diary 🔮
    -1002015119912,  # The Jungle
    -1002109171736,  # Luffy Chan Gambles 🎲
    -1001555991286,  # Bros [ ECA all safe ]
    -1002097913884,  # lced's House of Degeneracy
    -1001992057930,  # BASED DEGEN GEMS
    -1001979216488,  # SabsJournal
    -1001471369538,  # Gollum's Gems
    -1002325242919,  # _Jacob community 💬
]

# 服务器信息（用于标识）
SERVER_ID = 'server_d'
