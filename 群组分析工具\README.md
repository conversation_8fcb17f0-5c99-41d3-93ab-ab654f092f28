# 📊 群组活跃度分析工具

## 🎯 功能简介

这是一个专门用于分析Telegram群组活跃度的工具，可以：

- **分析所有合约**：找出推送最多合约的活跃群组
- **分析指定合约**：查看特定合约在哪些群组中出现最频繁
- **批量处理**：支持同时分析几十到上百个合约
- **实时进度**：显示处理进度和耗时统计

## 🚀 快速开始

### 1. 启动程序
双击 `start.bat` 文件即可启动

### 2. 选择分析模式

#### 模式一：分析所有合约（默认）
- 不在"合约筛选"区域输入任何内容
- 直接选择包含每日报告文件的文件夹
- 查看所有合约的群组活跃度排名

#### 模式二：分析指定合约
- 在"合约筛选"区域输入要分析的合约地址（每行一个）
- 或点击"📁 从文件导入"导入合约地址文件
- 选择包含每日报告文件的文件夹
- 查看这些合约在哪些群组中出现最多

### 3. 查看结果
- 群组按推送次数排序显示
- 包含群组名称、ID、监控Bot、推送次数等信息
- 可保存详细的JSON格式报告

## 📁 文件说明

- **`group_analyzer.py`** - 主程序文件
- **`start.bat`** - 启动脚本（推荐使用）
- **`使用说明.txt`** - 详细使用指南
- **`示例合约地址.txt`** - 3个示例合约，用于快速测试
- **`大批量测试合约.txt`** - 45个测试合约，用于性能测试

## ⚡ 性能表现

- **小批量**（<50个合约）：几乎瞬间完成
- **中等批量**（50-200个合约）：3-8秒完成
- **大批量**（200-1000个合约）：5-15秒完成
- **超大批量**（>1000个合约）：几十秒完成

## 📊 分析结果包含

### 基本统计
- 分析的文件数量和合约数量
- 发现的活跃群组数量
- 分析耗时

### 群组排名
- 群组名称和ID
- 负责监控的Bot
- 总推送次数
- 涉及的合约数量
- 涉及的文件数量

### 统计摘要
- 最活跃的群组
- 总推送次数
- 不重复合约数量

## 🔧 系统要求

- **Python 3.6+**
- **tkinter库**（通常随Python自带）
- **Windows系统**（批处理文件）

## 💡 使用技巧

### 1. 数据准备
- 确保每日报告文件为txt格式
- 文件内容需包含标准的群组和合约信息格式

### 2. 合约输入
- 每行输入一个合约地址
- 支持从txt文件批量导入
- 可以混合手动输入和文件导入

### 3. 结果分析
- 关注推送次数高的群组（活跃信号源）
- 关注涉及合约数多的群组（覆盖面广）
- 定期分析观察群组活跃度变化趋势

## 🛠️ 故障排除

### 常见问题

1. **双击bat文件没反应**
   - 确保Python已正确安装
   - 右键bat文件选择"以管理员身份运行"
   - 手动在命令行执行：`python group_analyzer.py`

2. **没有找到合约数据**
   - 检查合约地址格式是否正确
   - 确认选择的文件夹包含有效的报告文件
   - 检查报告文件的内容格式

3. **程序运行缓慢**
   - 大批量合约分析需要时间，请耐心等待
   - 可以分批处理减少单次分析量
   - 关注进度显示了解处理状态

### 性能优化建议

- 单次分析建议不超过1000个合约
- 定期清理无用的分析报告文件
- 确保有足够的磁盘空间

## 📈 实际应用场景

### 1. 信号源评估
输入最近热门的合约地址，找出最活跃的信号群组

### 2. 群组价值排名
分析特定类型合约（如meme币），评估不同群组的信号质量

### 3. 监控策略优化
根据分析结果调整监控群组的优先级和配置

### 4. 趋势分析
定期分析，观察群组活跃度的变化趋势

---

**这个工具可以帮助您更好地理解和优化合约监控策略，找出最有价值的信号源群组！** 🎯
