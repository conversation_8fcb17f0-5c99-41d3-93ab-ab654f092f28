# 监控系统分布式部署指南

## 📋 系统概述

本系统包含两个主要组件：
1. **4个发送Bot（监控程序）** - 部署在不同服务器上，监控Telegram群组，提取合约地址
2. **1个接收Bot（Flask服务器）** - 部署在单独服务器上，接收合约信息，应用过滤规则，推送到Telegram

## 🔧 系统架构

```
服务器A (monitor_1) ──┐
服务器B (monitor_2) ──┤ HTTP POST ──→ 接收服务器 (Flask) ──→ Telegram推送
服务器C (monitor_3) ──┤                    ↓
服务器D (monitor_4) ──┘              数据统计存储
```

## ✅ 已完成的配置匹配

### 1. 配置文件更新
所有4个服务器的 `config.py` 已添加：
```python
# HTTP推送配置
PUSH_URL = 'http://接收bot服务器IP:5000/push'  # 需要替换为实际IP
```

### 2. 监控程序更新
所有4个 `monitor.py` 已修改为：
- 导入 `requests` 和 `json` 模块
- 只使用HTTP推送（移除Telegram推送）
- 消息格式匹配接收bot期望格式

### 3. 消息格式匹配
- **发送格式**: `Time: MM-DD HH:MM:SS Group: 群组ID Monitor: 监控ID Server: 服务器ID Ca::合约地址`
- **接收格式**: 接收bot期望的 `Ca::` 格式

## 🚀 部署步骤

### 第一步：部署接收Bot

1. **选择一台服务器作为接收服务器**
2. **上传接收bot文件**：
   ```bash
   # 上传 sever-bot 目录到接收服务器
   scp -r sever-bot/ user@接收服务器IP:/path/to/deploy/
   ```

3. **启动接收bot**：
   ```bash
   cd sever-bot
   python3 server.py
   # 或使用screen后台运行
   screen -S receiver_bot python3 server.py
   ```

### 第二步：配置监控程序

1. **各服务器的配置文件已更新**：
   ```python
   # 在 server_a/config.py, server_b/config.py, server_c/config.py, server_d/config.py 中
   PUSH_URL = 'http://*************:5000/push'  # 已配置完成
   ```

### 第三步：部署监控程序

1. **服务器A部署**：
   ```bash
   # 上传 server_a 目录
   cd server_a
   python3 monitor.py
   ```

2. **服务器B部署**：
   ```bash
   # 上传 server_b 目录
   cd server_b
   python3 monitor.py
   ```

3. **服务器C部署**：
   ```bash
   # 上传 server_c 目录
   cd server_c
   python3 monitor.py
   ```

4. **服务器D部署**：
   ```bash
   # 上传 server_d 目录
   cd server_d
   python3 monitor.py
   ```

## 🔍 测试验证

### 1. 检查接收bot状态
```bash
curl http://*************:5000/health
```

### 2. 手动测试推送
```bash
curl -X POST http://*************:5000/push \
  -H "Content-Type: application/json" \
  -d '{"msg": "Time: 08-04 15:30:00 Group: -1001234567890 Monitor: test_monitor Server: test_server Ca::7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr"}'
```

### 3. 使用测试脚本
```bash
python3 test_http_connection.py *************
```

### 3. 查看日志
- **接收bot日志**：接收服务器控制台输出
- **监控程序日志**：各服务器目录下的 `.log` 文件

## 📊 推送规则

接收bot会根据以下规则决定是否推送到Telegram：

1. **单分钟超过7次** - 立即推送
2. **连续2分钟每分钟≥5次** - 立即推送
3. **10分钟窗口** - 超过10分钟未达标则永不推送

## 🔧 配置说明

### 接收Bot配置 (`sever-bot/server.py`)
```python
BOT_TOKEN = '**********:AAGsISnTldMzQWiX9Wk7j_WUqO7ckeMvjwI'
CHAT_ID = '**********'  # 你的私聊ID
TIME_WINDOW = 10 * 60   # 10分钟窗口
```

### 监控程序配置 (各服务器 `config.py`)
```python
PUSH_URL = 'http://*************:5000/push'  # 已配置完成
SERVER_BOT_USERNAME = '@your_server_bot'  # 保留，用于启动通知
```

## 🛠️ 故障排除

### 1. 接收bot无法启动
- 检查端口5000是否被占用：`netstat -tlnp | grep 5000`
- 确认依赖已安装：`pip install flask requests`
- 检查防火墙是否开放5000端口

### 2. 监控程序无法连接
- 检查PUSH_URL配置是否正确（IP地址和端口）
- 确认接收bot已启动并监听5000端口
- 测试网络连通性：`telnet 接收服务器IP 5000`
- 检查服务器间防火墙设置

### 3. 消息格式错误
- 确认消息包含 `Ca::` 格式
- 检查时间格式：`MM-DD HH:MM:SS`
- 查看接收bot日志确认收到的消息格式

### 4. 推送不工作
- 检查Telegram Bot Token是否正确
- 确认Chat ID是否正确
- 查看接收bot控制台日志
- 测试bot是否能正常发送消息

## 📁 文件结构

```
部署结构：
接收服务器/
└── sever-bot/
    ├── server.py      # 接收bot
    └── start_server_screen.sh

服务器A/
└── server_a/
    ├── config.py      # 配置文件（已更新）
    └── monitor.py     # 监控程序（已更新）

服务器B/
└── server_b/
    ├── config.py      # 配置文件（已更新）
    └── monitor.py     # 监控程序（已更新）

服务器C/
└── server_c/
    ├── config.py      # 配置文件（已更新）
    └── monitor.py     # 监控程序（已更新）

服务器D/
└── server_d/
    ├── config.py      # 配置文件（已更新）
    └── monitor.py     # 监控程序（已更新）
```

## 🎯 部署清单

### ✅ 已完成的配置
1. **接收服务器IP地址**: `*************`
2. **所有监控程序的PUSH_URL已配置**：
   ```python
   PUSH_URL = 'http://*************:5000/push'
   ```
3. **需要确保接收服务器防火墙开放5000端口**
4. **需要确保各监控服务器能访问接收服务器的5000端口**

### 🚀 启动顺序
1. **先启动接收bot**（接收服务器）
2. **再启动各监控程序**（4台监控服务器）
3. **验证连通性和推送功能**

## 💡 重要提示

- **只使用HTTP推送**，移除了Telegram推送避免重复
- **分布式部署**，每个组件独立运行在不同服务器
- **网络连通性**是关键，确保监控服务器能访问接收服务器
- **接收bot有健康监控**，长时间无消息会发送提醒
- **建议先测试网络连通性**再部署程序
