#!/bin/bash

# 检查是否存在旧的screen会话
if screen -ls | grep -q "monitor_c"; then
    echo "发现正在运行的monitor_c会话，正在关闭..."
    screen -S monitor_c -X quit
fi

# 创建新的screen会话并运行监控程序
screen -dmS monitor_c python3 monitor.py

# 等待几秒确保程序启动
sleep 2

# 检查是否成功启动
if screen -ls | grep -q "monitor_c"; then
    echo "✓ 监控程序已在screen会话中成功启动"
    echo "正在自动进入screen会话..."
    echo "提示: 使用 Ctrl+A 然后按 D 可以分离screen会话"
    echo "使用 'screen -r monitor_c' 可以重新连接会话"
    echo ""
    echo "按任意键进入screen会话，或按 Ctrl+C 取消..."
    read -n 1 -s

    # 自动进入screen会话
    screen -r monitor_c
else
    echo "✗ 监控程序启动失败，请检查错误信息"
fi
