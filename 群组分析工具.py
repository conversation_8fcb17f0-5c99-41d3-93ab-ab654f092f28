#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
群组分析工具 - 双击运行版本
功能：分析每日报告文件，找出最活跃的群组
使用方法：双击运行此文件
"""

import os
import re
import json
from datetime import datetime
from collections import defaultdict
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog

class GroupAnalyzer:
    def __init__(self):
        self.group_stats = defaultdict(lambda: {
            'group_name': '',
            'group_id': '',
            'monitor_bots': set(),
            'total_contracts': 0,
            'total_pushes': 0,
            'dates': set(),
            'contracts': set()
        })
        
    def analyze_folder(self, folder_path):
        """分析文件夹中的所有报告文件"""
        if not os.path.exists(folder_path):
            return False, "文件夹不存在"
        
        # 清空之前的统计
        self.group_stats.clear()
        
        # 获取所有txt文件
        txt_files = []
        for filename in os.listdir(folder_path):
            if filename.endswith('.txt'):
                txt_files.append(filename)
        
        if not txt_files:
            return False, "文件夹中没有找到txt文件"
        
        # 解析每个文件
        total_contracts = 0
        for filename in txt_files:
            file_path = os.path.join(folder_path, filename)
            contracts_count = self.parse_report_file(file_path, filename)
            total_contracts += contracts_count
        
        if total_contracts == 0:
            return False, "没有找到有效的合约数据"
        
        return True, f"成功分析了 {len(txt_files)} 个文件，共 {total_contracts} 个合约"
    
    def parse_report_file(self, file_path, filename):
        """解析单个报告文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            contracts_count = 0
            current_contract = None
            current_monitor = None
            
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                
                # 提取合约地址
                if '💎 合约地址:' in line:
                    contract_match = re.search(r'💎 合约地址:\s*([A-Za-z0-9]{32,50})', line)
                    if contract_match:
                        current_contract = contract_match.group(1)
                        contracts_count += 1
                
                # 提取监控bot信息
                elif line.startswith('🤖') and current_contract:
                    monitor_match = re.search(r'🤖\s*(\w+):', line)
                    if monitor_match:
                        current_monitor = monitor_match.group(1)
                
                # 提取群组信息
                elif line.startswith('•') and current_contract and current_monitor:
                    group_match = re.search(r'•\s*\d{2}:\d{2}:\d{2}\s*-\s*(.+?)\s*\((-?\d+)\)', line)
                    if group_match:
                        group_name = group_match.group(1).strip()
                        group_id = group_match.group(2)
                        
                        # 更新群组统计
                        key = f"{group_id}_{group_name}"
                        self.group_stats[key]['group_name'] = group_name
                        self.group_stats[key]['group_id'] = group_id
                        self.group_stats[key]['monitor_bots'].add(current_monitor)
                        self.group_stats[key]['total_pushes'] += 1
                        self.group_stats[key]['dates'].add(filename)
                        self.group_stats[key]['contracts'].add(current_contract)
            
            # 更新合约数量
            for key in self.group_stats:
                self.group_stats[key]['total_contracts'] = len(self.group_stats[key]['contracts'])
            
            return contracts_count
            
        except Exception as e:
            print(f"解析文件失败 {file_path}: {e}")
            return 0
    
    def get_results(self):
        """获取分析结果"""
        if not self.group_stats:
            return []
        
        # 转换为列表并排序
        results = []
        for key, stats in self.group_stats.items():
            results.append({
                'group_name': stats['group_name'],
                'group_id': stats['group_id'],
                'monitor_bots': list(stats['monitor_bots']),
                'total_contracts': stats['total_contracts'],
                'total_pushes': stats['total_pushes'],
                'file_count': len(stats['dates'])
            })
        
        # 按总推送次数排序
        results.sort(key=lambda x: x['total_pushes'], reverse=True)
        return results

class GroupAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("群组分析工具")
        self.root.geometry("800x600")
        
        # 设置窗口图标和样式
        try:
            self.root.iconbitmap(default='')  # 可以添加图标文件
        except:
            pass
        
        self.analyzer = GroupAnalyzer()
        self.results = []
        
        self.setup_ui()
        
        # 启动时显示欢迎信息
        self.show_welcome()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="📊 群组活跃度分析工具", font=("微软雅黑", 18, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 说明文字
        desc_label = ttk.Label(main_frame, text="选择包含每日报告文件的文件夹，分析哪些群组最活跃", font=("微软雅黑", 10))
        desc_label.grid(row=1, column=0, pady=(0, 20))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(0, 20))
        
        # 大按钮样式
        style = ttk.Style()
        style.configure('Big.TButton', font=('微软雅黑', 12))
        
        self.select_btn = ttk.Button(button_frame, text="📁 选择文件夹", command=self.select_folder, style='Big.TButton')
        self.select_btn.pack(side=tk.LEFT, padx=(0, 15), ipadx=10, ipady=5)
        
        self.save_btn = ttk.Button(button_frame, text="💾 保存结果", command=self.save_results, style='Big.TButton', state='disabled')
        self.save_btn.pack(side=tk.LEFT, ipadx=10, ipady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 结果文本框
        self.result_text = scrolledtext.ScrolledText(result_frame, height=20, width=80, font=('Consolas', 10))
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def show_welcome(self):
        """显示欢迎信息"""
        welcome_text = """
🎉 欢迎使用群组活跃度分析工具！

📋 使用步骤：
1. 点击"📁 选择文件夹"按钮
2. 选择包含每日报告文件的文件夹
3. 工具会自动分析所有txt文件
4. 查看群组活跃度排名结果
5. 可选择保存详细结果到JSON文件

💡 提示：
- 支持分析所有txt格式的每日报告文件
- 结果按推送次数从高到低排序
- 显示每个群组的推送次数、涉及合约数等信息

🚀 点击"选择文件夹"开始分析吧！
        """
        self.result_text.insert(tk.END, welcome_text)
    
    def select_folder(self):
        """选择文件夹并开始分析"""
        folder_path = filedialog.askdirectory(title="选择包含每日报告文件的文件夹")
        
        if not folder_path:
            return
        
        # 清空结果显示
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "🔍 正在分析文件夹中的报告文件...\n\n")
        self.root.update()
        
        # 执行分析
        success, message = self.analyzer.analyze_folder(folder_path)
        
        if not success:
            messagebox.showerror("分析失败", message)
            self.result_text.insert(tk.END, f"❌ {message}\n")
            return
        
        # 获取结果
        self.results = self.analyzer.get_results()
        
        if not self.results:
            self.result_text.insert(tk.END, "❌ 没有找到有效的分析结果\n")
            return
        
        # 显示结果
        self.display_results(message)
        self.save_btn.config(state='normal')
    
    def display_results(self, scan_message):
        """显示分析结果"""
        self.result_text.delete(1.0, tk.END)
        
        # 基本信息
        self.result_text.insert(tk.END, "📊 群组活跃度分析结果\n")
        self.result_text.insert(tk.END, "="*60 + "\n\n")
        self.result_text.insert(tk.END, f"✅ {scan_message}\n")
        self.result_text.insert(tk.END, f"📈 发现活跃群组: {len(self.results)} 个\n\n")
        
        # 群组排名
        self.result_text.insert(tk.END, "🏆 群组活跃度排名:\n")
        self.result_text.insert(tk.END, "-"*60 + "\n")
        
        for i, group in enumerate(self.results, 1):
            self.result_text.insert(tk.END, f"#{i:2d} 📢 {group['group_name']}\n")
            self.result_text.insert(tk.END, f"     📍 群组ID: {group['group_id']}\n")
            self.result_text.insert(tk.END, f"     🤖 监控Bot: {', '.join(group['monitor_bots'])}\n")
            self.result_text.insert(tk.END, f"     📊 总推送次数: {group['total_pushes']} 次\n")
            self.result_text.insert(tk.END, f"     💎 涉及合约数: {group['total_contracts']} 个\n")
            self.result_text.insert(tk.END, f"     📁 涉及文件数: {group['file_count']} 个\n\n")
        
        # 统计摘要
        total_pushes = sum(g['total_pushes'] for g in self.results)
        total_contracts = len(set().union(*[set(self.analyzer.group_stats[f"{g['group_id']}_{g['group_name']}"]['contracts']) for g in self.results]))
        
        self.result_text.insert(tk.END, "📋 统计摘要:\n")
        self.result_text.insert(tk.END, f"   🔥 最活跃群组: {self.results[0]['group_name']} ({self.results[0]['total_pushes']} 次)\n")
        self.result_text.insert(tk.END, f"   📊 总推送次数: {total_pushes} 次\n")
        self.result_text.insert(tk.END, f"   💎 不重复合约: {total_contracts} 个\n")
        
        # 滚动到顶部
        self.result_text.see(1.0)
    
    def save_results(self):
        """保存结果"""
        if not self.results:
            messagebox.showwarning("提示", "没有分析结果可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".json",
            initialvalue=f"群组分析结果_{timestamp}.json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                # 准备保存的数据
                save_data = {
                    'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_groups': len(self.results),
                    'results': self.results
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("保存成功", f"结果已保存到:\n{filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件时出错:\n{e}")

def main():
    """主函数"""
    try:
        root = tk.Tk()
        app = GroupAnalyzerGUI(root)
        root.mainloop()
    except Exception as e:
        # 如果出现错误，显示错误信息
        import traceback
        error_msg = f"程序运行出错:\n{e}\n\n详细信息:\n{traceback.format_exc()}"
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("程序错误", error_msg)
        except:
            # 如果连对话框都显示不了，就打印到控制台
            print(error_msg)
            input("按回车键退出...")

if __name__ == "__main__":
    main()
