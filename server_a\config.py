#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器A配置 - 监控账号1
"""

# 监控账号1的API配置
API_ID = '********'
API_HASH = '4c18107f9479a9d2fd420f2b6f889b90'
SESSION_NAME = 'monitor_1_session'
ACCOUNT_ID = 'monitor_1'



# HTTP推送配置
PUSH_URL = 'http://75.127.12.103:5000/push'  # 接收bot的HTTP接口

# 监控账号1负责的群组（32个）
MONITOR_GROUPS = [
    -*************,  # Bullish Calls - Solana
    -*************,  # Doctore Degens
    -*************,  # Krop Club
    -*************,  # ZODIGEM - BOT SIGNAL
    -*************,  # SolHouse Signal
    -*************,  # Solscan Calls
    -*************,  # RobinHood Smart Money 🤖
    -*************,  # <PERSON><PERSON>ems Calls
    -*************,  # 🐋 Free Whale Signals 🐋
    -*************,  # Teh'Pump Calls 🟢
    -*************,  # Degen ¦¦ Gamble🎲
    -*************,  # Onchain Alpha Trench
    -*************,  # Solbix Community Calls
    -*************,  # Bit Apes Calls
    -*************,  # Trade with Unique's alpha 🖨
    -*************,  # Sultan Plays 🐳
    -*************,  # APPLE CALLZ🍏
    -*************,  # MC DONALD's CALLS 🍟
    -*************,  # Cryptoo_ coffee calls
    -1001900737447,  # 🔥 (SaVaS) 🔥 1000x Gems 💎 ETH & SOL
    -1001898671713,  # Jeet Among Us
    -1002677781471,  # Degeneracy By BullBNB
    -1002276696688,  # CrikeyCallz
    -1001951793332,  # HOTDOG Journal
    -1001975976600,  # MAXI DEGENS
    -1002043732296,  # HIT-MONKEY CALL
    -1002177594166,  # OxyZen Calls
    -1002257276567,  # LEGEND社区
    -1001162128920,  # Crypto Gems 🚀📈
    -1002231257106,  # JMEENIECALLS
    -1002420387755,  # Degen's History
    -1002063316862,  # Nakamoto's Gamble
]

# 服务器信息（用于标识）
SERVER_ID = 'server_a'
