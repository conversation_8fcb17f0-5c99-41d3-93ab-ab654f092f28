📊 群组活跃度分析工具 - 使用说明

🚀 快速开始：
1. 双击运行"群组分析工具.py"文件
2. 点击"📁 选择文件夹"按钮
3. 选择包含每日报告文件的文件夹
4. 查看分析结果
5. 可选择保存结果到JSON文件

📁 文件要求：
- 文件夹中包含txt格式的每日报告文件
- 文件内容格式需要包含群组信息和合约地址
- 支持分析多个报告文件

📊 分析结果：
- 群组活跃度排名（按推送次数排序）
- 每个群组的推送次数
- 每个群组涉及的合约数量
- 负责监控的Bot信息
- 统计摘要信息

💾 保存功能：
- 可将分析结果保存为JSON格式
- 包含完整的群组排名和统计信息
- 文件名自动包含时间戳

⚠️ 注意事项：
- 确保电脑已安装Python环境
- 如果双击无法运行，请在命令行中执行：python 群组分析工具.py
- 分析大量文件时请耐心等待

🔧 系统要求：
- Python 3.6或更高版本
- tkinter库（通常随Python自带）

📞 使用帮助：
如果遇到问题，请检查：
1. Python是否正确安装
2. 选择的文件夹是否包含有效的报告文件
3. 报告文件格式是否正确
