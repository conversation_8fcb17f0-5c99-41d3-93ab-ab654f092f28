#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合约群组分析工具
功能：分析输入的合约地址在哪些群组中出现最频繁
使用方法：python contract_analyzer.py
"""

import sqlite3
import json
import os
import sys
from datetime import datetime
from collections import defaultdict

class ContractAnalyzer:
    def __init__(self, db_path='sever-bot/contracts.db'):
        self.db_path = db_path
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            print("请确保接收服务器已运行并有数据")
            sys.exit(1)
    
    def analyze_contracts(self, contracts):
        """分析合约在群组中的出现频率"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清理合约地址
            clean_contracts = [c.strip() for c in contracts if c.strip()]
            if not clean_contracts:
                return None
            
            print(f"🔍 正在分析 {len(clean_contracts)} 个合约...")
            
            # 查询合约推送记录
            placeholders = ','.join(['?' for _ in clean_contracts])
            query = f'''
                SELECT 
                    group_id,
                    group_name,
                    monitor_id,
                    contract_address,
                    COUNT(*) as push_count,
                    MIN(push_time) as first_seen,
                    MAX(push_time) as last_seen
                FROM contract_pushes 
                WHERE contract_address IN ({placeholders})
                GROUP BY group_id, group_name, monitor_id, contract_address
                ORDER BY group_id, push_count DESC
            '''
            
            cursor.execute(query, clean_contracts)
            results = cursor.fetchall()
            
            if not results:
                print("❌ 在数据库中没有找到这些合约的记录")
                return None
            
            # 统计分析
            group_stats = defaultdict(lambda: {
                'group_name': '',
                'monitor_id': '',
                'total_pushes': 0,
                'unique_contracts': set(),
                'contracts_detail': []
            })
            
            contract_stats = defaultdict(lambda: {
                'total_groups': 0,
                'total_pushes': 0,
                'groups': []
            })
            
            for row in results:
                group_id, group_name, monitor_id, contract_address, push_count, first_seen, last_seen = row
                
                # 群组统计
                group_stats[group_id]['group_name'] = group_name
                group_stats[group_id]['monitor_id'] = monitor_id
                group_stats[group_id]['total_pushes'] += push_count
                group_stats[group_id]['unique_contracts'].add(contract_address)
                group_stats[group_id]['contracts_detail'].append({
                    'contract': contract_address,
                    'push_count': push_count,
                    'first_seen': datetime.fromtimestamp(first_seen).strftime('%Y-%m-%d %H:%M:%S'),
                    'last_seen': datetime.fromtimestamp(last_seen).strftime('%Y-%m-%d %H:%M:%S')
                })
                
                # 合约统计
                contract_stats[contract_address]['total_groups'] += 1
                contract_stats[contract_address]['total_pushes'] += push_count
                contract_stats[contract_address]['groups'].append({
                    'group_id': group_id,
                    'group_name': group_name,
                    'monitor_id': monitor_id,
                    'push_count': push_count
                })
            
            conn.close()
            
            # 转换为排序列表
            group_ranking = []
            for group_id, stats in group_stats.items():
                group_ranking.append({
                    'group_id': group_id,
                    'group_name': stats['group_name'],
                    'monitor_id': stats['monitor_id'],
                    'total_pushes': stats['total_pushes'],
                    'unique_contracts': len(stats['unique_contracts']),
                    'contracts_detail': stats['contracts_detail']
                })
            
            # 按总推送次数排序
            group_ranking.sort(key=lambda x: x['total_pushes'], reverse=True)
            
            # 合约覆盖率
            found_contracts = len(contract_stats)
            coverage_rate = (found_contracts / len(clean_contracts) * 100) if clean_contracts else 0
            missing_contracts = [c for c in clean_contracts if c not in contract_stats]
            
            return {
                'summary': {
                    'total_input_contracts': len(clean_contracts),
                    'found_contracts': found_contracts,
                    'coverage_rate': round(coverage_rate, 2),
                    'total_groups': len(group_stats),
                    'total_pushes': sum(stats['total_pushes'] for stats in group_stats.values())
                },
                'group_ranking': group_ranking,
                'contract_stats': dict(contract_stats),
                'missing_contracts': missing_contracts
            }
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def print_results(self, results):
        """打印分析结果"""
        if not results:
            return
        
        summary = results['summary']
        group_ranking = results['group_ranking']
        missing_contracts = results['missing_contracts']
        
        print("\n" + "="*80)
        print("📊 合约群组分析报告")
        print("="*80)
        
        # 基本统计
        print(f"📈 输入合约数量: {summary['total_input_contracts']}")
        print(f"✅ 找到合约数量: {summary['found_contracts']}")
        print(f"📊 覆盖率: {summary['coverage_rate']}%")
        print(f"🏠 涉及群组数量: {summary['total_groups']}")
        print(f"📤 总推送次数: {summary['total_pushes']}")
        
        # 群组排名
        print(f"\n🏆 群组活跃度排名 (前10名):")
        print("-"*80)
        for i, group in enumerate(group_ranking[:10], 1):
            print(f"#{i:2d} 📢 {group['group_name']}")
            print(f"     📍 群组ID: {group['group_id']}")
            print(f"     🤖 监控Bot: {group['monitor_id']}")
            print(f"     📊 总推送: {group['total_pushes']} 次")
            print(f"     💎 涉及合约: {group['unique_contracts']} 个")
            print()
        
        # 未找到的合约
        if missing_contracts:
            print(f"❌ 未找到的合约 ({len(missing_contracts)}个):")
            for contract in missing_contracts[:5]:  # 只显示前5个
                print(f"   {contract}")
            if len(missing_contracts) > 5:
                print(f"   ... 还有 {len(missing_contracts) - 5} 个")
        
        print("\n" + "="*80)
    
    def save_detailed_report(self, results, filename=None):
        """保存详细报告到文件"""
        if not results:
            return
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"contract_analysis_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"📁 详细报告已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")

def main():
    """主函数 - 傻瓜式操作界面"""
    print("🔍 合约群组分析工具")
    print("="*50)
    
    analyzer = ContractAnalyzer()
    
    while True:
        print("\n请选择输入方式:")
        print("1. 手动输入合约地址")
        print("2. 从文件读取合约地址")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == '1':
            # 手动输入
            print("\n请输入合约地址 (每行一个，输入空行结束):")
            contracts = []
            while True:
                contract = input().strip()
                if not contract:
                    break
                contracts.append(contract)
            
            if contracts:
                results = analyzer.analyze_contracts(contracts)
                analyzer.print_results(results)
                
                if results and input("\n是否保存详细报告? (y/n): ").lower() == 'y':
                    analyzer.save_detailed_report(results)
        
        elif choice == '2':
            # 从文件读取
            filename = input("请输入文件路径: ").strip()
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    contracts = [line.strip() for line in f if line.strip()]
                
                if contracts:
                    results = analyzer.analyze_contracts(contracts)
                    analyzer.print_results(results)
                    
                    if results and input("\n是否保存详细报告? (y/n): ").lower() == 'y':
                        analyzer.save_detailed_report(results)
                else:
                    print("❌ 文件中没有找到有效的合约地址")
                    
            except FileNotFoundError:
                print(f"❌ 文件不存在: {filename}")
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        
        elif choice == '3':
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
