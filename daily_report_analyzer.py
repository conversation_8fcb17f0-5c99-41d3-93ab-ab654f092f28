#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日报告分析工具
功能：分析daily_reports文件夹中的报告，找出最活跃的群组
使用方法：python daily_report_analyzer.py
"""

import os
import re
import json
from datetime import datetime, timedelta
from collections import defaultdict
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog

class DailyReportAnalyzer:
    def __init__(self):
        self.reports_dir = 'daily_reports'
        self.group_stats = defaultdict(lambda: {
            'group_name': '',
            'group_id': '',
            'monitor_bots': set(),
            'total_contracts': 0,
            'total_pushes': 0,
            'dates': set(),
            'contracts': set()
        })
        
    def scan_reports(self, days=7):
        """扫描最近N天的报告文件"""
        if not os.path.exists(self.reports_dir):
            return False, f"报告目录不存在: {self.reports_dir}"
        
        # 获取所有报告文件
        report_files = []
        for filename in os.listdir(self.reports_dir):
            if filename.startswith('pushed_contracts_') and filename.endswith('.txt'):
                # 提取日期
                date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
                if date_match:
                    file_date = datetime.strptime(date_match.group(1), '%Y-%m-%d')
                    cutoff_date = datetime.now() - timedelta(days=days)
                    if file_date >= cutoff_date:
                        report_files.append((filename, file_date))
        
        if not report_files:
            return False, f"在 {self.reports_dir} 中没有找到最近{days}天的报告文件"
        
        # 按日期排序
        report_files.sort(key=lambda x: x[1], reverse=True)
        
        # 解析每个报告文件
        total_contracts = 0
        for filename, file_date in report_files:
            file_path = os.path.join(self.reports_dir, filename)
            contracts_count = self.parse_report_file(file_path, file_date.strftime('%Y-%m-%d'))
            total_contracts += contracts_count
        
        return True, f"成功分析了 {len(report_files)} 个报告文件，共 {total_contracts} 个合约"
    
    def parse_report_file(self, file_path, date_str):
        """解析单个报告文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            contracts_count = 0
            current_contract = None
            
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                
                # 提取合约地址
                if line.startswith('💎 合约地址:'):
                    contract_match = re.search(r'💎 合约地址:\s*([A-Za-z0-9]{32,50})', line)
                    if contract_match:
                        current_contract = contract_match.group(1)
                        contracts_count += 1
                
                # 提取群组信息
                elif line.startswith('🤖') and current_contract:
                    # 格式: 🤖 monitor_1:
                    monitor_match = re.search(r'🤖\s*(\w+):', line)
                    if monitor_match:
                        current_monitor = monitor_match.group(1)
                
                elif line.startswith('•') and current_contract:
                    # 格式: • 14:15:10 - 🐋 Free Whale Signals 🐋 (-1002380594298)
                    group_match = re.search(r'•\s*\d{2}:\d{2}:\d{2}\s*-\s*(.+?)\s*\((-?\d+)\)', line)
                    if group_match:
                        group_name = group_match.group(1).strip()
                        group_id = group_match.group(2)
                        
                        # 更新群组统计
                        key = f"{group_id}_{group_name}"
                        self.group_stats[key]['group_name'] = group_name
                        self.group_stats[key]['group_id'] = group_id
                        self.group_stats[key]['monitor_bots'].add(current_monitor)
                        self.group_stats[key]['total_pushes'] += 1
                        self.group_stats[key]['dates'].add(date_str)
                        self.group_stats[key]['contracts'].add(current_contract)
            
            # 更新合约数量
            for key in self.group_stats:
                self.group_stats[key]['total_contracts'] = len(self.group_stats[key]['contracts'])
            
            return contracts_count
            
        except Exception as e:
            print(f"解析文件失败 {file_path}: {e}")
            return 0
    
    def get_analysis_results(self):
        """获取分析结果"""
        if not self.group_stats:
            return None
        
        # 转换为列表并排序
        results = []
        for key, stats in self.group_stats.items():
            results.append({
                'group_name': stats['group_name'],
                'group_id': stats['group_id'],
                'monitor_bots': list(stats['monitor_bots']),
                'total_contracts': stats['total_contracts'],
                'total_pushes': stats['total_pushes'],
                'active_days': len(stats['dates']),
                'avg_pushes_per_day': round(stats['total_pushes'] / len(stats['dates']), 1)
            })
        
        # 按总推送次数排序
        results.sort(key=lambda x: x['total_pushes'], reverse=True)
        
        return results

class DailyReportAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("每日报告群组分析工具")
        self.root.geometry("900x700")
        
        self.analyzer = DailyReportAnalyzer()
        self.results = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="📊 每日报告群组分析工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="分析设置", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 天数选择
        ttk.Label(control_frame, text="分析最近天数:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.days_var = tk.StringVar(value="7")
        days_combo = ttk.Combobox(control_frame, textvariable=self.days_var, values=["1", "3", "7", "14", "30"], width=10)
        days_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # 按钮
        ttk.Button(control_frame, text="🔍 开始分析", command=self.analyze_reports).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(control_frame, text="💾 保存结果", command=self.save_results).grid(row=0, column=3, padx=(0, 10))
        ttk.Button(control_frame, text="📁 选择目录", command=self.select_directory).grid(row=0, column=4)
        
        # 状态显示
        self.status_label = ttk.Label(control_frame, text=f"报告目录: {self.analyzer.reports_dir}")
        self.status_label.grid(row=1, column=0, columnspan=5, sticky=tk.W, pady=(10, 0))
        
        # 结果区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 结果显示
        self.result_text = scrolledtext.ScrolledText(result_frame, height=25, width=100)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def select_directory(self):
        """选择报告目录"""
        directory = filedialog.askdirectory(title="选择daily_reports目录")
        if directory:
            self.analyzer.reports_dir = directory
            self.status_label.config(text=f"报告目录: {directory}")
    
    def analyze_reports(self):
        """分析报告"""
        try:
            days = int(self.days_var.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的天数")
            return
        
        # 清空之前的结果
        self.analyzer.group_stats.clear()
        self.result_text.delete(1.0, tk.END)
        
        # 显示分析进度
        self.result_text.insert(tk.END, f"🔍 正在分析最近 {days} 天的报告...\n")
        self.root.update()
        
        # 执行分析
        success, message = self.analyzer.scan_reports(days)
        
        if not success:
            messagebox.showerror("错误", message)
            self.result_text.insert(tk.END, f"❌ {message}\n")
            return
        
        # 获取结果
        self.results = self.analyzer.get_analysis_results()
        
        if not self.results:
            self.result_text.insert(tk.END, "❌ 没有找到分析结果\n")
            return
        
        # 显示结果
        self.display_results(message)
    
    def display_results(self, scan_message):
        """显示分析结果"""
        self.result_text.delete(1.0, tk.END)
        
        # 基本信息
        self.result_text.insert(tk.END, "📊 每日报告群组分析结果\n")
        self.result_text.insert(tk.END, "="*80 + "\n\n")
        self.result_text.insert(tk.END, f"✅ {scan_message}\n")
        self.result_text.insert(tk.END, f"📈 发现活跃群组: {len(self.results)} 个\n\n")
        
        # 群组排名
        self.result_text.insert(tk.END, "🏆 群组活跃度排名 (按总推送次数):\n")
        self.result_text.insert(tk.END, "-"*80 + "\n")
        
        for i, group in enumerate(self.results, 1):
            self.result_text.insert(tk.END, f"#{i:2d} 📢 {group['group_name']}\n")
            self.result_text.insert(tk.END, f"     📍 群组ID: {group['group_id']}\n")
            self.result_text.insert(tk.END, f"     🤖 监控Bot: {', '.join(group['monitor_bots'])}\n")
            self.result_text.insert(tk.END, f"     📊 总推送次数: {group['total_pushes']} 次\n")
            self.result_text.insert(tk.END, f"     💎 涉及合约: {group['total_contracts']} 个\n")
            self.result_text.insert(tk.END, f"     📅 活跃天数: {group['active_days']} 天\n")
            self.result_text.insert(tk.END, f"     📈 日均推送: {group['avg_pushes_per_day']} 次/天\n\n")
        
        # 统计摘要
        total_pushes = sum(g['total_pushes'] for g in self.results)
        total_contracts = sum(g['total_contracts'] for g in self.results)
        
        self.result_text.insert(tk.END, "\n📋 统计摘要:\n")
        self.result_text.insert(tk.END, f"   🔥 最活跃群组: {self.results[0]['group_name']} ({self.results[0]['total_pushes']} 次推送)\n")
        self.result_text.insert(tk.END, f"   📊 总推送次数: {total_pushes} 次\n")
        self.result_text.insert(tk.END, f"   💎 总合约数量: {total_contracts} 个 (可能有重复)\n")
    
    def save_results(self):
        """保存结果"""
        if not self.results:
            messagebox.showwarning("警告", "没有分析结果可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".json",
            initialvalue=f"group_analysis_{timestamp}.json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"结果已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

def main():
    root = tk.Tk()
    app = DailyReportAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
