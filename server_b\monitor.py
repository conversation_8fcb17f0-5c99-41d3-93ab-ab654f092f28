#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器B - 监控账号2
独立部署版本
使用方法: python monitor.py
"""

import asyncio
import re
import time
import logging
import sys
import os
import sqlite3
import glob
import requests
import json
from datetime import datetime, timedelta
from logging.handlers import TimedRotatingFileHandler
from telethon import TelegramClient, events
from telethon.errors import FloodWaitError
from config import *

class SingleMonitor:
    def __init__(self):
        # 创建客户端
        self.client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
        
        # 统计信息
        self.stats = {
            'total_messages': 0,
            'contracts_found': 0,
            'sent_to_server': 0,
            'duplicates_skipped': 0,
            'errors': 0,
            'start_time': datetime.now()
        }

        # 本地备份
        self.backup_contracts = []

        # 设置日志
        self.setup_logging()

        # 去重数据库
        self.db_path = f'{ACCOUNT_ID}_sent_contracts.db'
        self.init_database()

        # 清理过期日志文件
        self.cleanup_old_logs()

    def init_database(self):
        """初始化去重数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建已发送合约表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sent_contracts (
                    contract_address TEXT,
                    group_id INTEGER,
                    sent_at TEXT,
                    PRIMARY KEY (contract_address, group_id)
                )
            ''')

            # 清理7天前的记录
            cutoff_time = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute('DELETE FROM sent_contracts WHERE sent_at < ?', (cutoff_time,))

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 去重数据库初始化完成: {self.db_path}")

        except Exception as e:
            self.logger.error(f"❌ 数据库初始化失败: {e}")

    def is_contract_sent(self, contract_address, group_id):
        """检查合约是否已发送过"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                'SELECT 1 FROM sent_contracts WHERE contract_address = ? AND group_id = ?',
                (contract_address, group_id)
            )

            result = cursor.fetchone()
            conn.close()

            return result is not None

        except Exception as e:
            self.logger.error(f"❌ 检查重复失败: {e}")
            return False

    def mark_contract_sent(self, contract_address, group_id):
        """标记合约已发送"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO sent_contracts
                (contract_address, group_id, sent_at)
                VALUES (?, ?, ?)
            ''', (contract_address, group_id, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"❌ 标记发送失败: {e}")

    def setup_logging(self):
        """设置日志 - 按天分割"""
        # 创建日志目录
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 创建logger
        self.logger = logging.getLogger(ACCOUNT_ID)
        self.logger.setLevel(logging.DEBUG)  # 临时启用DEBUG级别

        # 清除已有的handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)  # 临时启用DEBUG级别
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 按天分割的文件日志
        log_filename = os.path.join(log_dir, f'{ACCOUNT_ID}.log')
        file_handler = TimedRotatingFileHandler(
            filename=log_filename,
            when='midnight',
            interval=1,
            backupCount=7,  # 保留7天的日志
            encoding='utf-8',
            delay=False,
            utc=False
        )
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)

        # 设置日志文件名格式 (添加日期后缀)
        file_handler.suffix = "%Y-%m-%d"
        file_handler.extMatch = re.compile(r"^\d{4}-\d{2}-\d{2}$")

        self.logger.addHandler(file_handler)

        # 防止日志重复
        self.logger.propagate = False

    def cleanup_old_logs(self):
        """清理超过7天的日志文件"""
        try:
            log_dir = 'logs'
            if not os.path.exists(log_dir):
                return

            # 获取当前时间
            now = datetime.now()
            cutoff_date = now - timedelta(days=7)

            # 查找所有日志文件
            log_pattern = os.path.join(log_dir, f'{ACCOUNT_ID}.log.*')
            log_files = glob.glob(log_pattern)

            deleted_count = 0
            for log_file in log_files:
                try:
                    # 从文件名提取日期
                    filename = os.path.basename(log_file)
                    if filename.count('.') >= 2:
                        date_part = filename.split('.')[-1]
                        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_part):
                            file_date = datetime.strptime(date_part, '%Y-%m-%d')
                            if file_date < cutoff_date:
                                os.remove(log_file)
                                deleted_count += 1
                                print(f"🗑️ 删除过期日志: {log_file}")
                except Exception as e:
                    print(f"⚠️ 删除日志文件失败 {log_file}: {e}")

            if deleted_count > 0:
                print(f"✅ 清理完成，删除了 {deleted_count} 个过期日志文件")
            else:
                print("📋 没有需要清理的过期日志文件")

        except Exception as e:
            print(f"❌ 日志清理失败: {e}")
        
    def extract_contract_address_from_text(self, text):
        """从文本中提取Solana合约地址"""
        if not text:
            return None

        # 首先尝试直接提取 Solana 合约地址
        solana_pattern = r'([1-9A-HJ-NP-Za-km-z]{32,50})'
        matches = re.findall(solana_pattern, text)

        for match in matches:
            if len(match) >= 32 and len(match) <= 50:
                if not any(exclude in match.lower() for exclude in ['http', 'www', 'com', 'org', 'net']):
                    return match

        # 只有在没有找到直接合约地址时，才检查 dexscreener.com 链接
        dexscreener_contract = self.extract_from_dexscreener_link(text)
        if dexscreener_contract:
            return dexscreener_contract

        return None

    def extract_from_dexscreener_link(self, text):
        """从 dexscreener.com 链接中提取合约地址"""
        try:
            import requests

            # 查找 dexscreener.com 链接
            dexscreener_pattern = r'https?://dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{32,50})'
            matches = re.findall(dexscreener_pattern, text, re.IGNORECASE)

            for pair_address in matches:
                # 通过 API 获取真实代币地址
                real_contract = self.get_token_from_dexscreener_api(pair_address)
                if real_contract:
                    self.logger.info(f"🔗 从DexScreener获取合约: {real_contract} (交易对: {pair_address})")
                    return real_contract

        except Exception as e:
            self.logger.warning(f"⚠️ DexScreener链接解析失败: {e}")

        return None

    def get_token_from_dexscreener_api(self, pair_address):
        """从DexScreener API获取真实代币地址"""
        try:
            import requests

            url = f"https://api.dexscreener.com/latest/dex/pairs/solana/{pair_address}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('pairs') and len(data['pairs']) > 0:
                    base_token = data['pairs'][0].get('baseToken', {}).get('address')
                    if base_token:
                        return base_token

        except Exception as e:
            self.logger.warning(f"⚠️ DexScreener API请求失败: {e}")

        return None
    
    async def send_to_server(self, contract_address, source_group_id, timestamp):
        """发送合约信息到HTTP接口"""
        try:
            # 获取群组名称
            group_name = await self.get_group_name(source_group_id)

            # 构造符合接收bot期望的消息格式
            time_str = datetime.fromtimestamp(timestamp).strftime("%m-%d %H:%M:%S")
            msg = f"Time: {time_str} Group: {source_group_id} GroupName: {group_name} Monitor: {ACCOUNT_ID} Server: {SERVER_ID} Ca::{contract_address}"

            payload = {
                'msg': msg
            }

            # 使用requests发送HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(PUSH_URL, json=payload, timeout=10)
            )

            if response.status_code == 200:
                self.stats['sent_to_server'] += 1
                self.logger.info(f"✅ HTTP推送成功: {contract_address[:8]}... 群组: {source_group_id}")
                return True
            else:
                self.logger.warning(f"⚠️ HTTP推送失败，状态码: {response.status_code}")
                raise Exception(f"HTTP推送失败，状态码: {response.status_code}")

        except Exception as e:
            self.logger.error(f"❌ HTTP推送失败: {e}")
            self.backup_contracts.append({
                'contract': contract_address,
                'group': source_group_id,
                'timestamp': timestamp,
                'retry_count': 0
            })
            return False
            self.stats['errors'] += 1
            return False
    
    async def retry_failed_contracts(self):
        """重试失败的合约"""
        if not self.backup_contracts:
            return
            
        self.logger.info(f"🔄 重试 {len(self.backup_contracts)} 个失败合约")
        
        retry_list = self.backup_contracts.copy()
        self.backup_contracts.clear()
        
        for contract_data in retry_list:
            if contract_data['retry_count'] < 3:
                contract_data['retry_count'] += 1
                success = await self.send_to_server(
                    contract_data['contract'],
                    contract_data['group'],
                    contract_data['timestamp']
                )
                if not success:
                    self.backup_contracts.append(contract_data)
            else:
                self.logger.error(f"❌ 重试失败，放弃: {contract_data['contract']}")
    
    async def status_monitor_task(self):
        """状态监控任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟

                # 重试失败的合约
                await self.retry_failed_contracts()

                # 打印统计信息
                self.print_stats()

            except Exception as e:
                self.logger.error(f"❌ 状态监控失败: {e}")
    
    def print_stats(self):
        """打印统计信息"""
        uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        self.logger.info(f"""
📊 [{ACCOUNT_ID}@{SERVER_ID}] 统计信息:
⏱️  运行时间: {uptime/3600:.1f} 小时
📨 总消息数: {self.stats['total_messages']}
🔍 发现合约: {self.stats['contracts_found']}
📤 发送成功: {self.stats['sent_to_server']}
⏭️ 跳过重复: {self.stats['duplicates_skipped']}
❌ 错误次数: {self.stats['errors']}
📋 待重试: {len(self.backup_contracts)}
📊 监控群组: {len(MONITOR_GROUPS)} 个
        """)
    
    def setup_message_handler(self):
        """设置消息处理器"""
        @self.client.on(events.NewMessage(chats=MONITOR_GROUPS))
        async def handle_message(event):
            try:
                self.stats['total_messages'] += 1

                message = event.message
                chat_id = event.chat_id

                # 调试日志：显示收到的消息
                self.logger.debug(f"📨 收到消息: 群组={chat_id} 内容={message.text[:100] if message.text else 'None'}...")

                # 每10条消息输出一次统计
                if self.stats['total_messages'] % 10 == 0:
                    self.logger.info(f"📊 已处理 {self.stats['total_messages']} 条消息，发现 {self.stats['contracts_found']} 个合约")

                # 提取合约地址
                contract_address = self.extract_contract_address_from_text(message.text)
                
                if contract_address:
                    self.stats['contracts_found'] += 1

                    # 检查是否已发送过
                    if self.is_contract_sent(contract_address, chat_id):
                        self.stats['duplicates_skipped'] += 1
                        self.logger.info(f"⏭️ 跳过重复合约: {contract_address} 群组: {chat_id}")
                        return

                    timestamp = int(time.time())
                    self.logger.info(f"🔍 发现新合约: {contract_address} 群组: {chat_id}")

                    # 发送到服务器
                    success = await self.send_to_server(contract_address, chat_id, timestamp)

                    # 如果发送成功，标记为已发送
                    if success:
                        self.mark_contract_sent(contract_address, chat_id)
                
            except Exception as e:
                self.stats['errors'] += 1
                self.logger.error(f"❌ 处理消息失败: {e}")
    
    async def get_joined_groups(self):
        """获取账号实际加入的群组信息"""
        joined_monitor_groups = {}
        joined_other_groups = {}

        try:
            # 获取所有对话
            async for dialog in self.client.iter_dialogs():
                if dialog.is_group or dialog.is_channel:
                    chat_id = dialog.id
                    try:
                        # 获取群组详细信息
                        entity = await self.client.get_entity(chat_id)
                        group_name = entity.title if hasattr(entity, 'title') else str(chat_id)

                        # 检查是否在监控列表中
                        if chat_id in MONITOR_GROUPS:
                            joined_monitor_groups[chat_id] = group_name
                        else:
                            joined_other_groups[chat_id] = group_name

                    except Exception as e:
                        if chat_id in MONITOR_GROUPS:
                            joined_monitor_groups[chat_id] = f"群组_{chat_id}"

            # 显示已加入的监控群组
            if joined_monitor_groups:
                self.logger.info("已加入的监控群组:")
                for group_id, group_name in joined_monitor_groups.items():
                    self.logger.info(f"  {group_id}: {group_name}")

            # 显示已加入但未监控的群组
            if joined_other_groups:
                self.logger.info("已加入未监控的群组:")
                for group_id, group_name in joined_other_groups.items():
                    self.logger.info(f"  {group_id}: {group_name}")

            return joined_monitor_groups

        except Exception as e:
            self.logger.error(f"获取群组信息失败: {e}")
            return {}

    async def get_group_name(self, group_id):
        """获取群组名称"""
        try:
            entity = await self.client.get_entity(group_id)
            return entity.title if hasattr(entity, 'title') else f"群组_{group_id}"
        except Exception as e:
            self.logger.debug(f"获取群组名称失败 {group_id}: {e}")
            return f"群组_{group_id}"

    async def start(self):
        """启动监控"""
        try:
            self.logger.info(f"启动监控: {ACCOUNT_ID} @ {SERVER_ID}")

            # 连接客户端
            await self.client.start()
            self.logger.info("客户端连接成功")

            # 获取实际加入的群组
            joined_groups = await self.get_joined_groups()

            # 设置消息处理器
            self.setup_message_handler()

            # 启动状态监控任务
            asyncio.create_task(self.status_monitor_task())

            self.logger.info("监控启动完成，开始监听消息...")

            # 保持运行
            await self.client.run_until_disconnected()

        except Exception as e:
            self.logger.error(f"启动失败: {e}")
            raise

async def main():
    """主函数"""
    try:
        monitor = SingleMonitor()
        await monitor.start()
        
    except KeyboardInterrupt:
        print(f"\n🛑 收到中断信号，{ACCOUNT_ID} 正在关闭...")
    except Exception as e:
        print(f"❌ {ACCOUNT_ID} 启动失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
