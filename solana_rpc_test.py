#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Solana RPC API调用
"""

import requests
import json

def analyze_transaction_addresses(result):
    """精确分析交易中的地址角色"""

    # 系统程序地址列表
    SYSTEM_PROGRAMS = {
        '11111111111111111111111111111111': 'System Program',
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'SPL Token Program',
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
        'ComputeBudget111111111111111111111111111111': 'Compute Budget Program',
        'SysvarRent111111111111111111111111111111111': 'Sysvar Rent',
        'SysvarC1ock11111111111111111111111111111111': '<PERSON>ysvar Clock',
    }

    transaction = result.get('transaction', {})
    message = transaction.get('message', {})
    meta = result.get('meta', {})

    # 获取账户列表
    account_keys = message.get('accountKeys', [])

    # 获取余额变化
    pre_balances = meta.get('preBalances', [])
    post_balances = meta.get('postBalances', [])

    print(f"\n🔍 详细地址分析:")
    print("=" * 60)

    user_addresses = []
    system_addresses = []

    for i, account in enumerate(account_keys):
        address = account if isinstance(account, str) else account.get('pubkey', '')

        # 计算余额变化
        balance_change = 0
        if i < len(pre_balances) and i < len(post_balances):
            balance_change = post_balances[i] - pre_balances[i]

        # 判断地址类型
        if address in SYSTEM_PROGRAMS:
            system_addresses.append({
                'address': address,
                'type': SYSTEM_PROGRAMS[address],
                'index': i
            })
        else:
            user_addresses.append({
                'address': address,
                'balance_change': balance_change,
                'index': i,
                'pre_balance': pre_balances[i] if i < len(pre_balances) else 0,
                'post_balance': post_balances[i] if i < len(post_balances) else 0
            })

    # 显示用户地址（按余额变化排序）
    print(f"👤 用户地址 ({len(user_addresses)} 个):")
    user_addresses.sort(key=lambda x: x['balance_change'], reverse=True)

    for addr in user_addresses:
        balance_change_sol = addr['balance_change'] / 1_000_000_000  # 转换为SOL
        change_indicator = "📈" if addr['balance_change'] > 0 else "📉" if addr['balance_change'] < 0 else "➖"

        role = ""
        if addr['balance_change'] < -1000000:  # 损失超过0.001 SOL
            role = " (可能是发送方/付费方)"
        elif addr['balance_change'] > 1000000:  # 获得超过0.001 SOL
            role = " (可能是接收方)"
        elif addr['balance_change'] < 0:
            role = " (支付手续费)"

        print(f"  {change_indicator} {addr['address']}")
        print(f"     余额变化: {balance_change_sol:+.9f} SOL{role}")
        print(f"     前: {addr['pre_balance']/1_000_000_000:.9f} SOL → 后: {addr['post_balance']/1_000_000_000:.9f} SOL")
        print()

    # 显示系统地址
    print(f"🔧 系统程序地址 ({len(system_addresses)} 个):")
    for addr in system_addresses:
        print(f"  • {addr['type']}")
        print(f"    {addr['address']}")
        print()

    # 分析指令来确定交易类型
    instructions = message.get('instructions', [])
    print(f"📋 交易指令分析:")
    for i, instruction in enumerate(instructions):
        program_id_index = instruction.get('programIdIndex', -1)
        if program_id_index < len(account_keys):
            program_address = account_keys[program_id_index]
            program_address = program_address if isinstance(program_address, str) else program_address.get('pubkey', '')
            program_name = SYSTEM_PROGRAMS.get(program_address, f"Unknown Program ({program_address[:8]}...)")
            print(f"  {i+1}. {program_name}")

    # 尝试识别主要参与者
    print(f"\n🎯 主要参与者识别:")

    # 找到余额变化最大的地址
    if user_addresses:
        biggest_loser = min(user_addresses, key=lambda x: x['balance_change'])
        biggest_gainer = max(user_addresses, key=lambda x: x['balance_change'])

        if biggest_loser['balance_change'] < -1000000:  # 损失超过0.001 SOL
            print(f"💸 主要发送方: {biggest_loser['address']}")
            print(f"   损失: {biggest_loser['balance_change']/1_000_000_000:.9f} SOL")

        if biggest_gainer['balance_change'] > 1000000:  # 获得超过0.001 SOL
            print(f"💰 主要接收方: {biggest_gainer['address']}")
            print(f"   获得: {biggest_gainer['balance_change']/1_000_000_000:.9f} SOL")

        # 找到支付手续费的地址（通常是交易发起者）
        fee_payers = [addr for addr in user_addresses if addr['balance_change'] < 0 and addr['balance_change'] > -1000000]
        if fee_payers:
            fee_payer = min(fee_payers, key=lambda x: x['balance_change'])
            print(f"🔑 交易发起者: {fee_payer['address']}")
            print(f"   手续费: {abs(fee_payer['balance_change'])/1_000_000_000:.9f} SOL")

def test_solana_rpc():
    """测试Solana RPC API调用"""
    
    # 交易签名
    tx_signature = "3FQX2TGa9xqj2q4NimEzkkxiaonv2BiSqutmV4V6Gxp7ESY6qu8gx6NXcS9qfABNSSbKJY6G5zhn152rKTiVVBnn"
    
    # RPC端点
    rpc_url = "https://api.mainnet-beta.solana.com"
    
    # 请求负载
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTransaction",
        "params": [
            tx_signature,
            {
                "encoding": "json",
                "maxSupportedTransactionVersion": 0
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=" * 80)
    print("测试Solana RPC API调用")
    print("=" * 80)
    print(f"交易签名: {tx_signature}")
    print(f"RPC端点: {rpc_url}")
    print()
    
    try:
        print("🔄 发送RPC请求...")
        response = requests.post(rpc_url, json=payload, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('result'):
                print("✅ 成功获取交易数据!")
                
                result = data['result']
                
                # 基本信息
                print(f"\n📋 交易基本信息:")
                print(f"  Slot: {result.get('slot', 'N/A')}")
                print(f"  Block Time: {result.get('blockTime', 'N/A')}")
                
                if result.get('meta'):
                    meta = result['meta']
                    print(f"  Fee: {meta.get('fee', 'N/A')} lamports")
                    print(f"  Status: {'Success' if meta.get('err') is None else 'Failed'}")
                
                # 精确分析钱包地址
                analyze_transaction_addresses(result)
                
                # 显示部分原始数据
                print(f"\n📄 原始数据结构预览:")
                print(json.dumps(data, indent=2)[:500] + "...")
                
            elif data.get('error'):
                print(f"❌ RPC错误: {data['error']}")
            else:
                print("❌ 未获取到结果数据")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_solana_rpc()
