@echo off
chcp 65001 >nul
cls
echo.
echo ████████████████████████████████████████
echo █                                      █
echo █        群组分析工具启动器            █
echo █                                      █
echo ████████████████████████████████████████
echo.

REM 检查Python文件是否存在
if not exist "群组分析工具.py" (
    echo ❌ 错误：找不到"群组分析工具.py"文件
    echo.
    echo 💡 请确保以下文件在同一文件夹中：
    echo    - 群组分析工具.py
    echo    - 运行工具.cmd
    echo.
    pause
    exit /b 1
)

echo 🔍 正在检查Python环境...

REM 尝试不同的Python命令
set PYTHON_CMD=
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    goto :run_program
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    goto :run_program
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    goto :run_program
)

REM 如果都找不到Python
echo ❌ 错误：未检测到Python环境
echo.
echo 💡 请先安装Python：
echo    1. 访问 https://www.python.org/downloads/
echo    2. 下载并安装Python 3.6或更高版本
echo    3. 安装时勾选"Add Python to PATH"
echo    4. 重启电脑后再试
echo.
echo 🔧 或者尝试以下解决方案：
echo    1. 在Microsoft Store中搜索并安装Python
echo    2. 确认Python已添加到系统PATH环境变量
echo.
pause
exit /b 1

:run_program
echo ✅ 找到Python环境：%PYTHON_CMD%
echo.
echo 🚀 正在启动群组分析工具...
echo    (如果程序没有响应，请稍等片刻)
echo.

REM 运行Python程序
%PYTHON_CMD% "群组分析工具.py"

REM 检查程序退出状态
if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错 (错误代码: %errorlevel%)
    echo.
    echo 💡 可能的解决方案：
    echo    1. 确保Python版本为3.6或更高
    echo    2. 检查是否缺少必要的Python库
    echo    3. 尝试右键以管理员身份运行此文件
    echo    4. 检查"群组分析工具.py"文件是否完整
    echo.
    echo 🔧 手动运行命令：
    echo    %PYTHON_CMD% "群组分析工具.py"
    echo.
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)
