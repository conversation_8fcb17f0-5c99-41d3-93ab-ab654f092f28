@echo off
cls
echo.
echo ========================================
echo.
echo        Group Analysis Tool
echo.
echo ========================================
echo.

REM Check if Python file exists
if not exist "群组分析工具.py" (
    echo Error: Cannot find Python file
    echo.
    echo Please make sure these files are in the same folder:
    echo    - 群组分析工具.py
    echo    - 运行工具.cmd
    echo.
    pause
    exit /b 1
)

echo Checking Python environment...

REM 尝试不同的Python命令
set PYTHON_CMD=
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    goto :run_program
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    goto :run_program
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    goto :run_program
)

REM If Python not found
echo Error: Python not detected
echo.
echo Please install Python first:
echo    1. Visit https://www.python.org/downloads/
echo    2. Download and install Python 3.6 or higher
echo    3. Check "Add Python to PATH" during installation
echo    4. Restart computer and try again
echo.
echo Alternative solutions:
echo    1. Install Python from Microsoft Store
echo    2. Make sure Python is added to system PATH
echo.
pause
exit /b 1

:run_program
echo Found Python: %PYTHON_CMD%
echo.
echo Starting Group Analysis Tool...
echo    (Please wait if the program doesn't respond immediately)
echo.

REM Run Python program
%PYTHON_CMD% "群组分析工具.py"

REM Check program exit status
if %errorlevel% neq 0 (
    echo.
    echo Program error (Exit code: %errorlevel%)
    echo.
    echo Possible solutions:
    echo    1. Make sure Python version is 3.6 or higher
    echo    2. Check if required Python libraries are installed
    echo    3. Try running as administrator
    echo    4. Check if Python file is complete
    echo.
    echo Manual command:
    echo    %PYTHON_CMD% "群组分析工具.py"
    echo.
    pause
) else (
    echo.
    echo Program exited normally
)
